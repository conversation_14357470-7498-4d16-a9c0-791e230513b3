import { BoundExpr, Field, Function } from "#/binder";
import { stripCast } from "#/binder/bind";
import {
  getExprScalarType,
  getExprSchema,
  isObjectLike,
  weakestScalarType,
} from "#/binder/types";
import { C<PERSON>house, LogicalSchema, ScalarType } from "#/schema";
import { ClickhouseTableDefinition, ClickhouseType } from "#/schema/clickhouse";
import { ident, join, snippet, sql, ToSQL } from "./snippet";
import { planSQLExpr } from "./sql";
import { unpivotTableAlias } from "./unpivot";
import { fail, PlanContextEngine } from "./util";

export type ClickhousePlanCtx = PlanContextEngine<ClickhouseTableDefinition>;

interface PlanOpts {
  noCast?: boolean;
}

export function planClickhouseExpr(
  ctx: ClickhousePlanCtx,
  expr: BoundExpr,
  opts?: PlanOpts,
): ToSQL {
  switch (expr.op) {
    case "literal":
      const literalType = getExprScalarType(expr);
      // Array literals are cast to jsonb, so we stringify the array rather than
      // passing it in as an actual javascript array param.
      let literalValue =
        literalType === "array"
          ? sql`${JSON.stringify(expr.value)}`
          : sql`${expr.value}`;
      if (literalType === "datetime") {
        literalValue = applyCast(literalValue, "string", "datetime");
      } else if (literalType === "boolean") {
        literalValue = expr.value ? sql`true` : sql`false`; // Otherwise will be serialized as a string
      }
      return literalValue;
    case "field":
      if (!ctx.table) {
        fail(ctx, "Field reference outside of table", expr);
      }

      if (expr.name.length == 0) {
        fail(ctx, `Table reference`, expr);
      }
      const first = expr.name[0];

      const pieces: (string | number)[] = [];
      let colType: ClickhouseType;
      if (expr.source && "unpivot" in expr.source) {
        pieces.push(unpivotTableAlias(expr.source.unpivot));
        if (expr.source.type === "key") {
          pieces.push(1);
        } else if (expr.source.type === "value") {
          pieces.push(2);
        }
        colType = { type: "String" };
      } else if (ctx.table.schema.columns[first]) {
        if (ctx.table.alias !== null) {
          pieces.push(ctx.table.alias);
        }
        const materializedMapPath =
          ctx.table.schema.columns[first].materializedMapPath;
        if (
          materializedMapPath &&
          (expr.name.length > 1 ||
            (expr.source &&
              "kind" in expr.source &&
              expr.source.kind === "unpivot-arg"))
        ) {
          pieces.push(...materializedMapPath);
          pieces.push(...expr.name.slice(1));
          colType = Clickhouse.record(Clickhouse.scalar("String"));
        } else {
          pieces.push(...ctx.table.schema.columns[first].path);
          pieces.push(...expr.name.slice(1));
          colType = ctx.table.schema.columns[first].type;
        }
      } else {
        fail(ctx, `Unknown field: ${first}`, expr);
      }

      const psliceToSQL = (pslice: (number | string)[]) =>
        join(
          pslice.map((p) =>
            typeof p === "string" ? ident(p) : sql`${snippet(`${p}`)}`,
          ),
          ".",
        );

      if (pieces.length <= 2) {
        let ret: ToSQL = psliceToSQL(pieces);
        const colSchema = chTypeToLogicalSchema(colType);
        const colScalarType = weakestScalarType(colSchema);
        const exprScalarType = weakestScalarType(expr.type);
        const fieldCast =
          colScalarType === exprScalarType ? null : exprScalarType;

        if (
          fieldCast !== null &&
          fieldCast !== "string" &&
          fieldCast !== "unknown"
        ) {
          ret = applyCast(ret, "string", fieldCast);
        }
        return ret;
      }

      const rootIdent = psliceToSQL(pieces.slice(0, 2));
      if (colType.type === "Map") {
        const colSql = sql`${rootIdent}[${pieces[2]}]`;
        const exprScalarType = weakestScalarType(expr.type);
        const colScalarType = weakestScalarType(
          chTypeToLogicalSchema(colType.value),
        );

        if (colScalarType === "string") {
          const jsonParts = pieces.slice(3).map(makePieceFragment);
          const fullPath = join([colSql, ...jsonParts], ", ");
          if (opts?.noCast) {
            return sql`${fullPath}`;
          } else if (isObjectLike(exprScalarType)) {
            return sql`NULLIF(NULLIF(JSONExtractRaw(${fullPath}), ''), 'null')`;
          } else {
            return sql`JSONExtract(${fullPath}, ${jsonTypeToCHType(exprScalarType)})`;
          }
        }

        if (pieces.length > 3) {
          fail(ctx, `Unsupported nested map access on non-string field`, expr);
        }

        const fieldCast =
          colScalarType === exprScalarType ? null : exprScalarType;
        if (fieldCast !== null) {
          return applyCast(colSql, colScalarType, fieldCast);
        } else {
          return colSql;
        }
      } else {
        const fieldType = weakestScalarType(expr.type);
        const jsonParts = pieces.slice(2).map(makePieceFragment);
        const path = join(jsonParts, ", ");
        if (isObjectLike(fieldType)) {
          // For some inane reason, Clickhouse returns an empty string for NULL
          return sql`NULLIF(NULLIF(JSONExtractRaw(${rootIdent}, ${path}), ''), 'null')`;
        } else {
          return sql`JSONExtract(${rootIdent}, ${path}, ${jsonTypeToCHType(
            fieldType,
          )})`;
        }
      }
    case "function":
      return planCHFunction(ctx, expr);
    case "cast":
      const inner = planClickhouseExpr(ctx, expr.expr);
      return applyCast(inner, getExprScalarType(expr.expr), expr.type);
    case "includes":
      const haystackType = getExprSchema(expr.haystack);
      const haystackScalarType = weakestScalarType(haystackType);
      if (isObjectLike(haystackScalarType)) {
        // TODO: Ideally, we should keep track of the SQL type as we compile it, so we know whether this is
        // an array or not.
        const haystack = makeClickhouseArray(ctx, expr.haystack, false);
        const needle = makeClickhouseArray(ctx, expr.needle, true);
        if (haystack !== null && needle !== null) {
          return sql`hasAny(${haystack}, ${needle})`;
        } else {
          const makeArray = (expr: BoundExpr): ToSQL =>
            sql`JSONExtractArrayRaw(COALESCE(${planClickhouseExpr(
              ctx,
              expr,
            )}, 'null'))`;

          return sql`hasAny(${makeArray(expr.haystack)}, ${makeArray(
            expr.needle,
          )})`;
        }
      } else {
        // This isn't exactly going to work, because there's no true "needle contains" type operator in Clickhouse
        // for arbitrary JSON
        return sql`${planClickhouseExpr(
          ctx,
          expr.haystack,
        )} ILIKE CONCAT('%', ${planClickhouseExpr(ctx, expr.needle)}, '%')`;
      }
    case "isnull":
    case "isnotnull":
      if (canSkipCast(ctx, expr.expr)) {
        // The metadata map pulls out undefined fields as '' and null fields as 'null'.
        const left = planClickhouseExpr(ctx, expr.expr, { noCast: true });
        const isNullExpr = sql`((${left} = '') OR (${left} = 'null'))`;
        return expr.op === "isnull" ? isNullExpr : sql`(NOT ${isNullExpr})`;
      }
      return planSQLExpr(ctx, expr, planClickhouseExpr);
    case "eq":
    // Although it would be nice to enable this case, because nulls are extracted as '', null value != x no longer
    // returns null, so results start to change.
    // case "ne":
    case "like":
    case "ilike":
      if (canSkipCast(ctx, expr.left)) {
        let op;
        switch (expr.op) {
          case "like":
            op = "like";
            break;
          case "ilike":
            op = "ILIKE";
            break;
          case "eq":
            op = "=";
            break;
        }
        // If this is an equality predicate, then we can make sure not to cast the LHS, and cast the RHS
        // to a JSON string, which allows us to use indices if present.
        const left = planClickhouseExpr(ctx, expr.left, { noCast: true });
        let right = planClickhouseExpr(ctx, expr.right);
        if (!isObjectLike(getExprScalarType(expr.right))) {
          right = sql`toJSONString(${right})`;
        }
        return sql`${left} ${snippet(op)} ${right}`;
      }
      return planSQLExpr(ctx, expr, planClickhouseExpr);
    default:
      return planSQLExpr(ctx, expr, planClickhouseExpr);
  }
}

export function makeClickhouseArray(
  ctx: ClickhousePlanCtx,
  expr: BoundExpr,
  allowNonArray: boolean,
): ToSQL | null {
  const stripped = stripCast(expr);
  const scalarType = getExprScalarType(expr);
  if (
    expr.op === "field" &&
    (allowNonArray || scalarType === "array") &&
    expr.name.length === 1 &&
    ctx.table?.schema.columns[expr.name[0]] !== undefined
  ) {
    const exprSql = planClickhouseExpr(ctx, expr);
    return scalarType === "array" ? exprSql : sql`[${exprSql}]`;
  } else if (stripped.op === "literal") {
    const value = stripped.value;
    let valueArray;
    if (!Array.isArray(value)) {
      if (!allowNonArray) {
        return null;
      }
      valueArray = [value];
    } else {
      valueArray = value;
    }
    return sql`[${join(
      valueArray.map((v) => sql`${v}`),
      ",",
    )}]`;
  } else {
    return null;
  }
}

function planCHFunction(ctx: ClickhousePlanCtx, func: Function): ToSQL {
  const args = func.args.map((a) => planClickhouseExpr(ctx, a));
  switch (func.name) {
    case "count":
    case "sum":
    case "avg":
    case "min":
    case "max":
      return sql`${ident(func.name)}(${join(args, ", ")})`;
    case "percentile":
      return sql`quantileExact(${args[1]} / 100)(${args[0]})`;
    case "second":
    case "minute":
    case "hour":
    case "day":
    case "week":
    case "month":
    case "year":
      let timestamp = sql`${args[0]}`;
      if (ctx.tzOffset) {
        // Adjust the input timestamp by subtracting the offset
        timestamp = sql`addMinutes(${timestamp}, ${-ctx.tzOffset})`;

        // Perform the date_trunc operation
        const truncatedTimestamp = sql`date_trunc(${func.name}, ${timestamp})`;

        // Adjust the result back to UTC
        timestamp = sql`addMinutes(${truncatedTimestamp}, ${ctx.tzOffset})`;
      } else {
        timestamp = sql`date_trunc(${func.name}, ${timestamp})`;
      }
      return sql`CAST(${timestamp}, 'Nullable(DateTime64)')`;
    case "current_timestamp":
      return sql`current_timestamp()`;
    case "current_date":
      return sql`current_date()`;
    case "sha256":
      return sql`sha256(${args[0]})`;
    case "insert":
      // NOTE: This is not going to work for most object types. We need to know whether
      // args[0] is a map, or something else, to generate the correct SQL. We approximate at
      // least the literal case with an empty map...
      let firstArg;
      let shouldCast;
      if (func.args[0].op === "literal") {
        firstArg = snippet("map()");
        // This is a total hack. It basically happens to correspond to the one case where we are calling
        // insert and COALESCE() it with a string JSON, so we need to cast it too.
        shouldCast = true;
      } else {
        firstArg = args[0];
        shouldCast = false;
      }
      const base = sql`mapConcat(${firstArg}, map(${join(args.slice(1), ", ")}))`;
      if (shouldCast) {
        return sql`toJSONString(${base})`;
      } else {
        return base;
      }
    case "coalesce":
      return sql`coalesce(${join(args, ", ")})`;
    case "least":
    case "greatest":
      return sql`arraySort(arrayFilter(x -> x IS NOT NULL, [${join(
        args,
        ", ",
      )}]))[${func.name === "least" ? 1 : -1}]`;
    case "nullif":
      return sql`nullIf(${args[0]}, ${args[1]})`;
    case "len":
      return sql`JSONArrayLength(${args[0]})`;
    case "concat":
    case "lower":
    case "upper":
    case "json_extract":
      // Long live clickhouse....
      return sql`null`;
    default:
      const _: never = func.name;
      throw new Error(`Unsupported function: ${_}`);
  }
}

function jsonTypeToCHType(type: ScalarType): string {
  switch (type) {
    case "string":
      return `Nullable(String)`;
    case "datetime":
      return `Nullable(DateTime64)`;
    case "date":
      return `Nullable(Date)`;
    case "number":
      return `Nullable(Float64)`; // Clickhouse Decimal requires a precision/scale
    case "integer":
      return `Nullable(Int64)`;
    case "boolean":
      return `Nullable(Boolean)`;
    case "object":
    case "array":
    case "unknown":
      return `Nullable(String)`;
    case "null":
      return `Nullable(String)`;
    case "interval":
      throw new Error("Clickhouse has a separate type for each interval");
  }
}

function chTypeToLogicalSchema(type: ClickhouseType): LogicalSchema {
  switch (type.type) {
    case "Nullable":
    case "LowCardinality":
      return chTypeToLogicalSchema(type.inner);
    case "UInt64":
    case "UInt32":
    case "UInt16":
    case "UInt8":
    case "Int64":
    case "Int32":
    case "Int16":
    case "Int8":
      return { type: "integer" };
    case "Boolean":
      return { type: "boolean" };
    case "Float64":
    case "Float32":
      return { type: "number" };
    case "DateTime":
    case "DateTime64":
      return { type: "string", format: "date-time" };
    case "Date":
      return { type: "string", format: "date" };
    case "String":
    case "FixedString":
      return { type: "string" };
    case "Map":
      const keyType = chTypeToLogicalSchema(type.key);
      if (keyType.type !== "string") {
        throw new Error(`Clickhouse Map key must be a string`);
      }
      return { additionalProperties: chTypeToLogicalSchema(type.value) };
    case "Array":
      return {
        type: "array",
        items: chTypeToLogicalSchema(type.items),
      };
    default:
      throw new Error(`Unsupported Clickhouse type: ${type.type}`);
  }
}

function applyCast(expr: ToSQL, exprType: ScalarType, targetType: ScalarType) {
  if (exprType === targetType) {
    return expr;
  } else if (exprType === "string" && targetType === "datetime") {
    return sql`parseDateTimeBestEffort(${expr})`;
  } else {
    // NOTE: Clickhouse expects the second argument to be a string, hence
    // the return type of jsonTypeToCHType (and lack of snippet())
    return sql`CAST(${expr}, ${jsonTypeToCHType(targetType)})`;
  }
}

function makePieceFragment(piece: string | number): ToSQL {
  // Clickhouse uses 1-based indexing for JSONExtract
  return sql`${typeof piece === "number" && piece >= 0 ? piece + 1 : piece}`;
}

export function isCHMaterializedMapAvailable(
  ctx: ClickhousePlanCtx,
  expr: Field,
): boolean {
  if (!ctx.table) {
    fail(ctx, "Field reference outside of table", expr);
  }
  if (expr.name.length == 0) {
    fail(ctx, `Table reference`, expr);
  }
  if (
    !expr.source &&
    expr.name.length > 1 &&
    (ctx.table.schema.columns[expr.name[0]]?.materializedMapPath ||
      ctx.table.schema.columns[expr.name[0]]?.type.type === "Map")
  ) {
    return true;
  }
  return false;
}

function canSkipCast(ctx: ClickhousePlanCtx, expr: BoundExpr): boolean {
  return !!(
    expr.op === "field" &&
    ctx.table?.schema.columns[expr.name[0]]?.materializedMapPath &&
    expr.name.length === 2
  );
}
