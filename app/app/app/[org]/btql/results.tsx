import { useMemo } from "react";
import { type RowData, Table } from "#/ui/arrow-table";
import { useViewStates } from "#/utils/view/use-view";
import prettyBytes from "pretty-bytes";
import { type BTQLResponse, parseBtqlSchema } from "#/utils/btql/btql";
import { DataType, type Field, type Schema } from "apache-arrow";
import { z } from "zod";
import { serializeJSONWithPlainString } from "#/utils/object";
import { relativeTimeMs } from "#/ui/date";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

export function QueryResults({
  scrollContainerRef,
  rawResult,
  error,
  lastQueryMs,
}: {
  rawResult: BTQLResponse<Record<string, unknown>> | null;
  error: string | null;
  lastQueryMs: number | null;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
}) {
  const viewProps = useViewStates({
    pageIdentifier: "btql",
    viewParams: undefined,
    clauseChecker: null,
  });

  const tableState = useMemo<{
    data: RowData[];
    fields: Field[] | undefined;
  }>(() => {
    if (!rawResult) {
      return {
        data: [],
        fields: undefined,
      };
    }

    // If the data does not have an id field, then add one
    const fullArrowSchema = parseBtqlSchema(null /*ignored*/, rawResult.schema);
    return {
      data: postProcessData(rawResult.data, fullArrowSchema),
      fields: fullArrowSchema.fields,
    };
  }, [rawResult]);

  const byteEstimate = useMemo(() => {
    if (!rawResult) {
      return null;
    }
    return JSON.stringify(rawResult.data).length;
  }, [rawResult]);

  const hasError = Boolean(error);
  const noResult = !Boolean(rawResult);
  if (noResult && !hasError) {
    return (
      <TableEmptyState
        label="Run query to view results"
        labelClassName="text-sm"
        className="mt-3 flex-1 py-8"
      />
    );
  }

  const emptyResults = rawResult?.data.length === 0;
  if (emptyResults && !hasError) {
    return (
      <TableEmptyState
        label="No results found"
        labelClassName="text-sm"
        className="mt-3 flex-1 py-8"
      />
    );
  }

  return (
    <Table
      {...tableState}
      viewProps={viewProps}
      scrollContainerRef={scrollContainerRef}
      isSortable={false}
      extraRightControls={
        <div className="flex h-7 flex-1 items-center justify-end gap-4 text-xs text-primary-500">
          {rawResult && (
            <div>{rawResult.data.length.toLocaleString()} rows</div>
          )}
          {byteEstimate !== null && <div>{prettyBytes(byteEstimate)}</div>}
          {lastQueryMs && <div>{relativeTimeMs(lastQueryMs)}</div>}
        </div>
      }
      tableType="detailed"
      error={error}
      skipErrorReporting
    />
  );
}

function postProcessData(
  data: Record<string, unknown>[],
  schema: Schema,
): RowData[] {
  return data.map((d, index) => {
    const id = z.string().safeParse(d.id);
    return {
      ...Object.fromEntries(
        Object.entries(d).map(([k, v]) => {
          const fieldSchema = schema.fields.find((f) => f.name === k);
          const val = DataType.isUtf8(fieldSchema)
            ? serializeJSONWithPlainString(v)
            : v;
          return [k, val];
        }),
      ),
      id: id.success
        ? id.data
        : d.id
          ? serializeJSONWithPlainString(d.id)
          : `${index}`,
    };
  });
}
