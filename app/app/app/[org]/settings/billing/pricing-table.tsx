import Link from "next/link";
import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";
import { PLAN_SLUGS } from "./plans";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ArrowRight } from "lucide-react";

export const PricingTable = ({
  isLanding,
  orgName,
}: {
  isLanding?: boolean;
  orgName?: string;
}) => {
  const settingsLink = getOrgSettingsLink({ orgName: orgName ?? "" });

  return (
    <div className="mb-16 flex flex-col lg:flex-row">
      <div className="flex flex-1 py-4">
        <div className="flex-1 rounded-lg p-6 pr-0 bg-primary-200 lg:rounded-r-none">
          <Plan isLanding={isLanding}>Free</Plan>
          <div className="text-4xl">$0 / month</div>
          <Feature
            isLanding={isLanding}
            title="1 million"
            description="Trace spans"
            className="mt-7"
          />
          <Feature
            isLanding={isLanding}
            title="1 GB"
            description="Processed data"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="10,000"
            description="Scores and custom metrics"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="14 days"
            description="Data retention"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="5"
            description="Users"
            className="mt-4"
          />
          {isLanding && (
            <Link
              href="/signup"
              className={cn(
                buttonVariants({ variant: "inverted" }),
                "mt-8 text-lg font-medium text-background",
              )}
            >
              Get started for free
            </Link>
          )}
        </div>
      </div>
      <div className="flex flex-1">
        <div
          className={cn("flex-1 rounded-lg p-6 lg:py-9 text-background", {
            "bg-limeOklch dark:bg-blueOklch dark:text-warm text-black":
              isLanding,
            "bg-gradient-to-br from-accent-600 to-accent-500 dark:from-accent-500 dark:to-accent-400":
              !isLanding,
          })}
        >
          <Plan isLanding={isLanding}>Pro</Plan>
          <div className="text-4xl">$249 / month</div>
          <Feature
            isLanding={isLanding}
            title="Unlimited"
            description="Trace spans"
            className="mt-7"
          />
          <Feature
            isLanding={isLanding}
            title="5 GB"
            description="Processed data ($3/GB thereafter)"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="50,000"
            description="Scores and custom metrics ($1.50/1,000 thereafter)"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="1 month"
            description="Data retention ($3/GB retained thereafter)"
          />
          <Feature
            isLanding={isLanding}
            title="5"
            description="Users ($49 per additional user)"
          />
          {!isLanding && (
            <Link
              href={`${settingsLink}/billing/payment?purchasePlanSlug=${PLAN_SLUGS.PRO}`}
              className={cn(
                buttonVariants({ variant: "default" }),
                "mt-8 font-medium text-primary-900",
              )}
            >
              Upgrade <ArrowRight className="size-4" />
            </Link>
          )}
          {isLanding && (
            <Link
              href="/signup"
              className={cn(
                buttonVariants({ variant: "inverted" }),
                "mt-8 text-lg font-medium text-background",
              )}
            >
              Get started
            </Link>
          )}
        </div>
      </div>
      <div className="flex flex-1 py-4">
        <div className="flex flex-1 flex-col items-start rounded-lg p-6 bg-primary-900 text-background lg:rounded-l-none">
          <Plan isLanding={isLanding}>Enterprise</Plan>
          <div className="text-4xl">Custom</div>
          <div className="mt-7 flex-1 text-pretty text-lg opacity-80">
            Premium support with on-prem or hosted deployment for high volume or
            privacy-sensitive data.
          </div>
          <Link
            href="/contact"
            className={cn(
              buttonVariants({ variant: "default" }),
              "mt-8 font-medium text-primary-900",
              {
                "text-lg": isLanding,
              },
            )}
          >
            Contact us
          </Link>
        </div>
      </div>
    </div>
  );
};

const Plan = ({
  children,
  className,
  isLanding,
}: {
  children: React.ReactNode;
  className?: string;
  isLanding?: boolean;
}) => {
  return (
    <h2
      className={cn(
        "text-xl font-semibold uppercase mb-4",
        {
          "font-normal": isLanding,
        },
        className,
      )}
    >
      {children}
    </h2>
  );
};

const Feature = ({
  title,
  description,
  className,
  isLanding,
}: {
  title: string;
  description: string;
  className?: string;
  isLanding?: boolean;
}) => (
  <div className={cn("mt-4", className)}>
    <div className="text-xl font-medium">{title}</div>
    <div
      className={cn("text-sm opacity-60", {
        "text-base": isLanding,
      })}
    >
      {description}
    </div>
  </div>
);
