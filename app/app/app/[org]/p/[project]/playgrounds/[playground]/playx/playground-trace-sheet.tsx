import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { type LoadedTrace } from "#/ui/trace/graph";
import {
  useExpandedRowWithComparisons,
  useRowAuditLog,
} from "#/ui/trace/query";
import {
  ScrollableContainerWithOptions,
  selectorForField,
} from "#/ui/trace/scrollable-container-with-options";
import { SpanContents } from "#/ui/trace/span-contents";
import { SpanName } from "#/ui/trace/span-header";
import { SpanSkeleton } from "#/ui/trace/span-skeleton";
import {
  CollapsibleTraceTree,
  type traceCollapseState,
  type TraceViewParams,
} from "#/ui/trace/trace";
import { useFullyCloseSidePanel } from "#/ui/trace/trace-panel";
import { useExtractScores } from "#/ui/trace/use-extract-scores";
import { useRowNavigation } from "#/ui/trace/use-row-navigation";
import { cn } from "#/utils/classnames";
import {
  diffKeynameForIndex,
  DiffRightField,
  flattenDiffObjects,
  getDiffLeft,
  isDiffObject,
  type RowId,
} from "#/utils/diffs/diff-objects";
import { isEmpty } from "#/utils/object";
import { type SpanData, type Span, type SpanMetrics } from "@braintrust/local";
import { getDatasetLink } from "../../../datasets/[dataset]/getDatasetLink";
import { type ProjectContextDataset } from "../../../project-actions";
import { PlaygroundRowSheet } from "../playground-row-sheet";
import {
  type SearchResultField,
  useTraceSearchGetters,
  useTraceSearchSetters,
} from "#/ui/trace/trace-search-context";
import {
  memo,
  type RefObject,
  type TransitionStartFunction,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { ProjectContext } from "../../../projectContext";
import { type DataDisplayedField } from "#/ui/trace/data-display";
import { type ProjectConfig } from "#/utils/score-config";
import {
  type MultiTraceContext,
  useTraceCopilotContext,
} from "#/ui/copilot/trace";
import useSpanSelection from "#/ui/trace/use-span-selection";
import { useHotkeysContext } from "react-hotkeys-hook";
import {
  getOriginDataset,
  type OriginDataset,
  useOriginTrace,
} from "#/ui/trace/origin-dataset/origin-dataset";
import { GoToOriginProvider } from "#/ui/trace/go-to-origin-context";
import {
  type CommentFn,
  type DeleteCommentFn,
  type UpdateRowFn,
} from "#/utils/mutable-object";
import { useDiffModeState, useTraceViewTypeState } from "#/ui/query-parameters";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ActionButton } from "#/ui/trace/ActionButton";
import { ArrowRightToLine, ListVideo, Search } from "lucide-react";
import { TRANSACTION_ID_FIELD } from "@braintrust/core";
import { Switch } from "#/ui/switch";
import { Button } from "#/ui/button";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/charts/colors";
import { GridExperimentHeader } from "#/ui/table/formatters/grid-layout-columns";
import { SpanField } from "#/ui/trace/use-span-field-order.tsx";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { TraceSearch } from "#/ui/trace/trace-search";
import { TraceSearchResults } from "#/ui/trace/trace-search-results";
import scrollIntoView from "scroll-into-view-if-needed";
import { usePanelSize } from "#/ui/use-panel-size";
import {
  streamingCompletionsAtom,
  useStreamingCompletionErrorAtom,
  useStreamingCompletionsAtom,
  isPlaygroundRunningAtom,
  latestXactIdAtom,
} from "./atoms";
import { useAtomValue, useSetAtom } from "jotai";
import { type GenerationId } from "./experiment-data";
import { SINGLETON_DATASET_ID } from "./stream";
import { type PlayXRunPromptsArgs } from "./playx";
import {
  makeRowIdPrimaryOrigin,
  matchesRowId,
} from "#/ui/table/use-active-row-effects";
import { SavingStatus, type SavingState } from "#/ui/saving";
import { type Virtualizer } from "@tanstack/react-virtual";
import { TraceTree } from "#/ui/trace/trace-tree";
import { type ModelCosts } from "#/ui/prompts/models";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { getTraceDurations } from "#/ui/trace/trace-utils";

type Props = {
  dataset?: ProjectContextDataset;
  isReadOnly: boolean;
  isRunning: boolean;
  onAddRow: VoidFunction;
  onStop: VoidFunction;
  orgName: string;
  rowIds: RowId[];
  savingState: SavingState;
} & Omit<
  InnerProps,
  | "rowId"
  | "isDelayedSpanChangeTransitioning"
  | "startSpanChangeTransition"
  | "runRef"
  | "setIsDirty"
>;

export default function PlaygroundTraceSheet({
  dataset,
  hasRun,
  isReadOnly,
  isRunning,
  onAddRow,
  onStop,
  orgName,
  rowIds,
  savingState,
  ...innerProps
}: Props) {
  const { enableScope, disableScope } = useHotkeysContext();
  useEffect(() => {
    enableScope("sidepanel");
    return () => {
      disableScope("sidepanel");
    };
  }, [enableScope, disableScope]);

  const {
    activeRowId,
    hasPrevRow,
    hasNextRow,
    onNextRow,
    onPrevRow,
    isDelayedSpanChangeTransitioning,
    startSpanChangeTransition,
  } = useRowNavigation({
    rowIds,
  });

  const datasetName = dataset?.name ?? "";
  const datasetProjectName = dataset?.project_name ?? "";
  const onClose = useFullyCloseSidePanel();

  const { setSearchOpen } = useTraceSearchSetters();

  const [_viewType, setViewType] = useTraceViewTypeState();

  const originDataset = useMemo(
    () => getOriginDataset(activeRowId),
    [activeRowId],
  );
  const [isDirty, setIsDirty] = useState(false);

  // The data fetching needed for the arguments for onRun is done in TraceSheetContent, since we want
  // to dynamically fetch when a row is selected, but PlaygroundRowSheet needs to be mounted to animate in/out.
  // Instead of setting some state here when TraceSheetContent has fetched the necessary data, triggering an extra
  // re-render, we can access a function which has closed over the required arguments via this ref.
  const runRef = useRef<{
    onRun: (colIdx?: number) => void;
  } | null>(null);

  const primaryRowId = makeRowIdPrimaryOrigin(activeRowId);
  const rowIndex = rowIds.findIndex((r) => matchesRowId(r, primaryRowId)) + 1;

  return (
    <PlaygroundRowSheet
      headerText={`Row ${rowIndex} of ${rowIds.length}`}
      isOpen={!!activeRowId}
      isRunning={isRunning}
      onRunRow={() => {
        if (isRunning) {
          onStop();
        } else {
          // If the playground has been run, run just this row
          runRef.current?.onRun();
        }
      }}
      onClose={onClose}
      onAddRow={onAddRow}
      isDirty={isDirty}
      hasPrevRow={hasPrevRow}
      hasNextRow={hasNextRow}
      onPrevRow={() => onPrevRow({ withTransition: true })}
      onNextRow={() => onNextRow({ withTransition: true })}
      isReadOnly={isReadOnly}
      datasetRowUrl={
        originDataset?.id === SINGLETON_DATASET_ID
          ? undefined
          : getDatasetLink({
              orgName,
              projectName: datasetProjectName,
              datasetName,
              rowId: originDataset?.rowId,
            })
      }
      datasetRowId={originDataset?.rowId}
      extraRightControls={
        <>
          <SavingStatus
            state={savingState}
            className="mr-2 text-xs text-gray-500"
          />
          <ActionButton
            hotkey={["Mod+F"]}
            buttonVariant="ghost"
            tooltipText="Find in row"
            icon={<Search className="size-3" />}
            actionHandler={() => {
              setSearchOpen((prev) => !prev);
              setViewType("trace");
            }}
          />
          <DiffSwitch
            onToggle={() => setViewType("trace")}
            rowId={activeRowId}
          />
        </>
      }
    >
      {activeRowId && (
        <TraceSheetContent
          {...innerProps}
          hasRun={hasRun}
          isDelayedSpanChangeTransitioning={isDelayedSpanChangeTransitioning}
          originDataset={originDataset}
          rowId={activeRowId}
          runRef={runRef}
          setIsDirty={setIsDirty}
          startSpanChangeTransition={startSpanChangeTransition}
        />
      )}
    </PlaygroundRowSheet>
  );
}

const COMPARISON_CLASS_NAMES_WITH_BASE = [
  "bg-primary-500",
  ...EXPERIMENT_COMPARISON_COLOR_CLASSNAMES,
];
const OBJECT_TYPE = "playground_logs";
const DATASET_EDITABLE_FIELDS: DataDisplayedField[] = [
  "input",
  "expected",
  "metadata",
];
const NO_EDITABLE_FIELDS: DataDisplayedField[] = [];
const DATASET_DEFAULT_COLLAPSED_FIELDS = [SpanField.ACTIVITY];
const AUDIT_LOG_READY: number[] = [];

export type PlaygroundTracePanelLayout = {
  dataset?: number;
  traceTree?: number;
};

type InnerProps = {
  hasRun: boolean;
  isDelayedSpanChangeTransitioning: boolean;
  modelSpecScan: string | null;
  originDataset?: OriginDataset;
  onApplySearch: ApplySearch;
  onDatasetComment: CommentFn;
  onDatasetDeleteComment: DeleteCommentFn;
  onRun: (args: PlayXRunPromptsArgs) => void;
  promptSessionId?: string;
  rowId: RowId;
  runRef: RefObject<{
    onRun: (colIdx?: number) => void;
  } | null>;
  playgroundName: string;
  promptDisplays: { id: string; name: string }[];
  startSpanChangeTransition: TransitionStartFunction;
  updateDatasetRow: UpdateRowFn;
  defaultPanelLayout: PlaygroundTracePanelLayout;
  generationIds: GenerationId[];
  setIsDirty: (isDirty: boolean) => void;
  allAvailableModelCosts?: Record<string, ModelCosts>;
};

function TraceSheetContent({
  isDelayedSpanChangeTransitioning,
  modelSpecScan,
  onApplySearch,
  onDatasetComment,
  onDatasetDeleteComment,
  onRun,
  originDataset,
  promptSessionId,
  rowId,
  runRef,
  playgroundName,
  promptDisplays,
  startSpanChangeTransition,
  updateDatasetRow,
  defaultPanelLayout,
  generationIds,
  setIsDirty,
  allAvailableModelCosts,
}: InnerProps) {
  const playgroundLogFetchEnabled =
    isDiffObject(rowId) && rowId.objectType !== "dataset";
  const {
    traces,
    traceRowIds,
    numTraces,
    hasLoaded: haveTracesLoaded,
    isPending: areTracesPending,
  } = useExpandedRowWithComparisons({
    enabled: playgroundLogFetchEnabled,
    objectId: promptSessionId,
    rowId,
    objectType: OBJECT_TYPE,
    modelSpecScan,
  });
  const { hasLoaded: hasOriginDatasetLoaded, trace: originDatasetTrace } =
    useOriginTrace({
      originDataset,
    });
  const { auditLogData } = useRowAuditLog({
    auditLogScan: null,
    auditLogReady: AUDIT_LOG_READY,
    rowId:
      originDataset?.rowId && originDataset?.rowId !== SINGLETON_DATASET_ID
        ? (originDataset.rowId ?? null)
        : null,
    dynamicObjectId:
      originDataset?.id && originDataset?.id !== SINGLETON_DATASET_ID
        ? (originDataset.id ?? null)
        : null,
    objectType: "dataset",
  });

  const { config: projectConfig, projectId } = useContext(ProjectContext);
  const copilotContext = useTraceCopilotContext({
    objectType: OBJECT_TYPE,
    objectName: playgroundName,
  });

  const placeholderTrace: LoadedTrace | null = useMemo(() => {
    if (!originDatasetTrace) return null;

    const traceRoot = {
      ...originDatasetTrace.root,
      data: {
        ...originDatasetTrace.root.data,
        span_attributes: { name: "eval" },
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        metrics: { start: Date.now() / 1000 } as SpanMetrics &
          Record<string, number>,
      },
    };
    const spans = Object.fromEntries(
      Object.keys(originDatasetTrace).map((key) => [key, traceRoot]),
    );
    return {
      ...originDatasetTrace,
      root: traceRoot,
      spans,
      isPlaceholder: true,
    };
  }, [originDatasetTrace]);
  const traceForTree = traces[0] ?? placeholderTrace;

  const { activeSpanId, setSelectedSpan } = useSpanSelection({
    copilotContext,
    startSpanChangeTransition,
    trace: traceForTree,
    loadedTrace: traceForTree,
  });
  const baseSelectedSpan =
    traceForTree && activeSpanId && traceForTree.spans[activeSpanId]
      ? traceForTree.spans[activeSpanId]
      : null;

  const { searchQuery, searchResultFields, resultIndex, isSearchOpen } =
    useTraceSearchGetters();

  const datasetRowXactId = originDatasetTrace?.root.data[TRANSACTION_ID_FIELD];
  const isDirty =
    // Prevent flashing when changing rows by only checking for dirty when the origin dataset and the loaded trace are for the same row
    originDataset?.rowId === originDatasetTrace?.root.id &&
    !!originDataset?.originXactId &&
    originDataset.rowId !== SINGLETON_DATASET_ID &&
    !!originDatasetTrace &&
    (datasetRowXactId! > originDataset?.originXactId ||
      // When the optimistic update for the updated row is created, the xact_id is "0"
      originDatasetTrace.root.data[TRANSACTION_ID_FIELD] === "0");
  useEffect(() => {
    setIsDirty(isDirty);
  }, [isDirty, setIsDirty]);

  const promptsWithTrace = useMemo(() => {
    let curTraceIndex = 0;
    return promptDisplays.map(({ name }, i) => {
      const diffKeyname = diffKeynameForIndex(i, true);
      const traceIndex =
        isDiffObject(rowId) && rowId[diffKeyname] ? curTraceIndex : undefined;
      if (traceIndex != null) {
        curTraceIndex++;
      }
      return {
        name,
        traceIndex,
      };
    });
  }, [promptDisplays, rowId]);

  const playgroundLogXactId = maxXactId(
    traces.map((t) =>
      t.isPlaceholder ? undefined : t.root.data[TRANSACTION_ID_FIELD],
    ),
  );
  useImperativeHandle(
    runRef,
    () => ({
      onRun: (colIdx?: number) => {
        if (
          !originDatasetTrace ||
          (playgroundLogFetchEnabled && areTracesPending)
        ) {
          return;
        }
        onRun(
          originDatasetTrace.root.id === SINGLETON_DATASET_ID
            ? {
                colIdx,
              }
            : {
                colIdx,
                datasetRow: {
                  rowId: originDatasetTrace.root.id,
                  updatePlaygroundRows: playgroundLogFetchEnabled
                    ? {
                        inputValue: extractRunPromptInput(
                          originDatasetTrace.root.data,
                        ),
                        ids: promptsWithTrace.map(({ traceIndex }) =>
                          traceIndex != null &&
                          (colIdx == null || colIdx === traceIndex)
                            ? traceRowIds[traceIndex]
                            : null,
                        ),
                        playgroundLogXactId,
                        _xactId:
                          originDatasetTrace.root.data[TRANSACTION_ID_FIELD],
                        created: originDatasetTrace.root.data.created,
                      }
                    : undefined,
                },
              },
        );
      },
    }),
    [
      originDatasetTrace,
      playgroundLogFetchEnabled,
      traceRowIds,
      promptsWithTrace,
      onRun,
      playgroundLogXactId,
      areTracesPending,
    ],
  );

  const [viewType] = useTraceViewTypeState();

  const [traceTreeCollapsed, setTraceTreeCollapsed] = useEntityStorage({
    entityType: "traceTree",
    entityIdentifier: projectId ?? "",
    key: "isCollapsed",
  });

  const [haveTracesLoadedOnce, setHaveTracesLoadedOnce] = useState(false);
  if (haveTracesLoaded && !haveTracesLoadedOnce) {
    setHaveTracesLoadedOnce(true);
  }
  const spanSkeletons = useMemo(
    () =>
      new Array(numTraces).fill(null).map((_, i) => (
        <div
          key={`skeleton-${i + 1}`}
          className={cn("flex-1 border-l px-4 py-3 min-w-[420px]", {
            "border-l-0": i === 0,
          })}
        >
          <SpanSkeleton />
        </div>
      )),
    [numTraces],
  );

  const originDatasetContent = useMemo(
    () =>
      originDatasetTrace &&
      originDatasetTrace.root.id !== SINGLETON_DATASET_ID ? (
        <>
          <div className="flex justify-between">
            <div className="flex h-7 items-center text-xs">
              Edit dataset row
            </div>
            {traceTreeCollapsed && (
              <ActionButton
                hotkey="\"
                actionHandler={() => setTraceTreeCollapsed(false)}
                tooltipText="Show trace tree"
                icon={<ArrowRightToLine className="size-3" />}
                label="Trace tree"
              />
            )}
          </div>
          <SpanContents
            key={originDatasetTrace.root.id}
            isDatasetRow
            isRoot
            span={originDatasetTrace.root}
            trace={originDatasetTrace}
            projectConfig={projectConfig}
            onApplySearch={onApplySearch}
            copilotContext={copilotContext}
            comparisonClassName=""
            resultIndex={resultIndex}
            searchResultFields={searchResultFields}
            searchQuery={searchQuery}
            objectType="dataset"
            editableFields={
              originDatasetTrace?.root?.id === SINGLETON_DATASET_ID
                ? NO_EDITABLE_FIELDS
                : DATASET_EDITABLE_FIELDS
            }
            hiddenFields={HIDDEN_DATASET_FIELDS}
            commentFn={onDatasetComment}
            deleteCommentFn={onDatasetDeleteComment}
            defaultCollapsedFields={DATASET_DEFAULT_COLLAPSED_FIELDS}
            updateRow={updateDatasetRow}
            auditLogData={
              originDatasetTrace?.root?.id === SINGLETON_DATASET_ID
                ? undefined
                : auditLogData
            }
            localStorageKeyPrefix={`${promptSessionId}-dataset`}
            diffMessage="Showing diff from comparison task -> base task"
            allAvailableModelCosts={allAvailableModelCosts}
          />
        </>
      ) : !hasOriginDatasetLoaded ? (
        <SpanSkeleton />
      ) : // TODO: null state
      null,
    [
      auditLogData,
      copilotContext,
      hasOriginDatasetLoaded,
      onApplySearch,
      onDatasetComment,
      onDatasetDeleteComment,
      originDatasetTrace,
      projectConfig,
      promptSessionId,
      resultIndex,
      searchQuery,
      searchResultFields,
      setTraceTreeCollapsed,
      traceTreeCollapsed,
      updateDatasetRow,
      allAvailableModelCosts,
    ],
  );

  const { allSpansMap, spanIdToCorrespondingBaseTraceSpanId } = useMemo(() => {
    const allSpans = [];
    const spanIdToCorrespondingBaseTraceSpanId = new Map<string, string>();

    for (const [i, trace] of traces.entries()) {
      if (i === 0) {
        allSpans.push(
          ...Object.values(trace.spans).map((span) => ({
            ...span,
            traceName: promptDisplays[i]?.name,
            traceClassName: COMPARISON_CLASS_NAMES_WITH_BASE[i],
          })),
        );
      } else {
        for (const [baseTraceSpanId, span] of Object.entries(trace.spans)) {
          spanIdToCorrespondingBaseTraceSpanId.set(span.id, baseTraceSpanId);
          allSpans.push({
            ...span,
            traceName: promptDisplays[i]?.name,
            traceClassName: COMPARISON_CLASS_NAMES_WITH_BASE[i],
          });
        }
      }
    }

    if (originDatasetTrace) {
      const originDatasetRootSpan = { ...originDatasetTrace.root };
      originDatasetRootSpan.data.span_attributes.name = "dataset";
      allSpans.push(originDatasetRootSpan);
    }

    return {
      allSpansMap: Object.fromEntries(
        allSpans.map((span) => [span.span_id, span]),
      ),
      spanIdToCorrespondingBaseTraceSpanId,
    };
  }, [originDatasetTrace, traces, promptDisplays]);

  const onGoToField = useCallback(
    ({ span, field }: { span: Span; field: string }) => {
      // If span has changed to a non-dataset span, set the selected span to the base trace span
      if (
        span.id !== baseSelectedSpan?.id &&
        span.id !== originDatasetTrace?.root?.id
      ) {
        const baseTraceSpanId = spanIdToCorrespondingBaseTraceSpanId.get(
          span.id,
        );
        if (baseTraceSpanId) {
          setSelectedSpan(traces[0].spans[baseTraceSpanId]);
        } else {
          setSelectedSpan(span);
        }
      }
      setTimeout(() => {
        const fieldElement = document.querySelector(selectorForField(field));
        if (!fieldElement) return;
        scrollIntoView(fieldElement, {
          scrollMode: "if-needed",
          block: "start",
        });
      }, 200);
    },
    [
      baseSelectedSpan,
      originDatasetTrace?.root?.id,
      setSelectedSpan,
      spanIdToCorrespondingBaseTraceSpanId,
      traces,
    ],
  );

  const isRunningOrStreaming = useAtomValue(isPlaygroundRunningAtom);

  const showSkeleton =
    (!isRunningOrStreaming && !haveTracesLoadedOnce) ||
    (isRunningOrStreaming && !hasOriginDatasetLoaded);

  const traceSearchResultsVirtualizer =
    useRef<Virtualizer<HTMLDivElement, Element>>(null);

  const traceTree = useMemo(
    () =>
      showSkeleton ? (
        <div className="flex-1 px-4 py-3">
          <SpanSkeleton />
        </div>
      ) : (
        <CollapsibleTraceTree
          comparisonClassName=""
          firstRootTitle="Root"
          label="Base trace"
          selectedSpan={baseSelectedSpan}
          setSelectedSpan={setSelectedSpan}
          setTraceTreeCollapsed={() => {
            setTraceTreeCollapsed(true);
          }}
          trace={traceForTree}
          searchResults={
            isSearchOpen &&
            searchQuery.length > 0 && (
              <TraceSearchResults
                virtualizerRef={traceSearchResultsVirtualizer}
                spans={allSpansMap}
                onGoToField={onGoToField}
              />
            )
          }
          objectType={OBJECT_TYPE}
          objectId={promptSessionId}
        />
      ),
    [
      showSkeleton,
      baseSelectedSpan,
      setSelectedSpan,
      traceForTree,
      isSearchOpen,
      searchQuery.length,
      allSpansMap,
      onGoToField,
      setTraceTreeCollapsed,
      promptSessionId,
    ],
  );

  const minDatasetPanelWidth = usePanelSize(280);
  const maxDatasetPanelWidth = usePanelSize(440);

  const minTraceTreePanelWidth = usePanelSize(200);
  const maxTraceTreePanelWidth = usePanelSize(400);
  const defaultTraceTreePanelWidth = usePanelSize(280);

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: PlaygroundTracePanelLayout = {
        ...defaultPanelLayout,
        dataset: sizes[0],
        traceTree: sizes[1],
      };
      document.cookie = `react-resizable-panels:playground-trace-sheet-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const canViewSpans = isRunningOrStreaming || playgroundLogFetchEnabled;
  const canViewTraceTree =
    !traceTreeCollapsed && canViewSpans && viewType === "trace";

  return (
    <GoToOriginProvider origin={traces[0]?.root.data.origin}>
      <ResizablePanelGroup
        autoSaveId="playgroundTraceSheetLayout"
        direction="horizontal"
        className="flex-1"
        onLayout={onPanelLayout}
      >
        {originDatasetContent && (
          <>
            <ResizablePanel
              order={0}
              className="relative flex"
              id="dataset"
              minSize={minDatasetPanelWidth}
              defaultSize={defaultPanelLayout.dataset ?? minDatasetPanelWidth}
              maxSize={maxDatasetPanelWidth}
            >
              <div className="flex flex-1 flex-col gap-4 overflow-auto px-4 py-3">
                {originDatasetContent}
              </div>
            </ResizablePanel>
            <ResizableHandle
              className={cn("bg-primary-200/70", {
                hidden: !canViewTraceTree,
              })}
            />
          </>
        )}
        <ResizablePanel
          order={1}
          className={cn("flex flex-col relative", {
            hidden: !canViewTraceTree,
          })}
          id="tree"
          minSize={minTraceTreePanelWidth}
          maxSize={maxTraceTreePanelWidth}
          defaultSize={
            defaultPanelLayout.traceTree ?? defaultTraceTreePanelWidth
          }
        >
          {isSearchOpen && baseSelectedSpan && (
            <TraceSearch
              spans={allSpansMap}
              onGoToField={onGoToField}
              virtualizerRef={traceSearchResultsVirtualizer}
              selectedSpanId={baseSelectedSpan.id}
            />
          )}
          {traceTree}
        </ResizablePanel>

        <ResizableHandle className="bg-primary-200/70" />
        <ResizablePanel order={2} className="relative flex">
          <div className="flex w-full overflow-x-scroll">
            {!canViewSpans ? (
              <div className="flex w-full p-4">
                <TableEmptyState
                  className="w-full justify-center"
                  Icon={ListVideo}
                  label="This row has not been run yet"
                />
              </div>
            ) : showSkeleton || !placeholderTrace ? (
              spanSkeletons
            ) : (
              promptsWithTrace.map(({ name, traceIndex }, i) => (
                <SpanDisplay
                  key={`trace-${i}`}
                  activeSpanId={activeSpanId}
                  comparisonClassName={COMPARISON_CLASS_NAMES_WITH_BASE[i]}
                  copilotContext={copilotContext}
                  isBase={i === 0}
                  isDelayedSpanChangeTransitioning={
                    isDelayedSpanChangeTransitioning
                  }
                  localStorageKeyPrefix={promptSessionId}
                  onApplySearch={onApplySearch}
                  projectConfig={projectConfig}
                  promptDisplayName={name}
                  resultIndex={resultIndex}
                  searchQuery={searchQuery}
                  searchResultFields={searchResultFields}
                  placeholderTrace={placeholderTrace}
                  playgroundLogTrace={
                    traceIndex != null ? traces[traceIndex] : undefined
                  }
                  generationId={generationIds[i]?.id}
                  isRunning={isRunningOrStreaming}
                  // Weird edge case when switching from a run row to an un-run row
                  // the trace data still contains the loaded data from the run row
                  // so when the un-run row runs for the first time,
                  // it will show the previous trace data for a moment.
                  // So show a placeholder in that case
                  showPlaceholder={!playgroundLogFetchEnabled}
                  colIdx={i}
                  runRef={runRef}
                  startSpanChangeTransition={startSpanChangeTransition}
                  promptSessionId={promptSessionId}
                />
              ))
            )}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </GoToOriginProvider>
  );
}

function maxXactId(xactIds: (string | undefined)[]) {
  let max = xactIds[0];
  for (const xactId of xactIds) {
    if (max == null) {
      max = xactId;
    } else if (xactId && max < xactId) {
      max = xactId;
    }
  }
  return max ?? "0";
}

const EDITABLE_FIELDS: DataDisplayedField[] = [];
const DEFAULT_FIELD_ORDER = [
  SpanField.METRICS,
  SpanField.SCORES,
  SpanField.ERROR,
  SpanField.INPUT,
  SpanField.OUTPUT,
  SpanField.EXPECTED,
  SpanField.METADATA,
];
const HIDDEN_ROOT_FIELDS: TraceViewParams["hiddenFields"] = [
  "input",
  "expected",
  "metadata",
];
const HIDDEN_DATASET_FIELDS: TraceViewParams["hiddenFields"] = [
  SpanField.METRICS,
];
const HIDDEN_NON_ROOT_FIELDS: TraceViewParams["hiddenFields"] = [];
const DEFAULT_COLLAPSED_FIELDS = [
  SpanField.METRICS,
  SpanField.SCORES,
  SpanField.ERROR,
  SpanField.INPUT,
  SpanField.EXPECTED,
  SpanField.METADATA,
];

type SpanProps = {
  activeSpanId: string | null;
  comparisonClassName: string;
  copilotContext: MultiTraceContext;
  isBase: boolean;
  isDelayedSpanChangeTransitioning: boolean;
  localStorageKeyPrefix?: string;
  onApplySearch: ApplySearch;
  projectConfig: ProjectConfig;
  promptDisplayName: string;
  resultIndex: number | null;
  searchQuery: string;
  searchResultFields: SearchResultField[];
  playgroundLogTrace: LoadedTrace | undefined;
  placeholderTrace: LoadedTrace;
  generationId?: string;
  isRunning?: boolean;
  showPlaceholder?: boolean;
  colIdx?: number;
  runRef: RefObject<{
    onRun: (colIdx: number) => void;
  } | null>;
  startSpanChangeTransition: TransitionStartFunction;
  promptSessionId: string | undefined;
};

const SpanDisplay = memo(SpanDisplayComponent);

function SpanDisplayComponent({
  activeSpanId,
  comparisonClassName,
  copilotContext,
  isBase,
  isDelayedSpanChangeTransitioning,
  localStorageKeyPrefix,
  onApplySearch,
  projectConfig,
  promptDisplayName,
  resultIndex,
  searchQuery,
  searchResultFields,
  playgroundLogTrace,
  placeholderTrace,
  generationId,
  isRunning,
  showPlaceholder,
  colIdx,
  runRef,
  startSpanChangeTransition,
  promptSessionId,
}: SpanProps) {
  const [viewType] = useTraceViewTypeState();
  const trace = useTraceWithStreamingOutput({
    datasetRowId: placeholderTrace.root.id,
    generationId,
    placeholderTrace,
    playgroundLogTrace,
    showPlaceholder,
  });
  const span = activeSpanId
    ? (trace.spans[activeSpanId] ?? trace.root)
    : trace.root;

  const hasError =
    !isEmpty(span?.data.error) &&
    (!isDiffObject(span?.data.error) ||
      !isEmpty(span?.data.error[DiffRightField]));

  const { autoScores } = useExtractScores({
    objectType: OBJECT_TYPE,
    projectConfigScores: projectConfig?.scores,
    span,
  });

  const isRoot = trace?.root.id === span?.id;

  const scrollableContainerRef = useRef<HTMLDivElement>(null);

  return (
    <ScrollableContainerWithOptions
      containerRef={scrollableContainerRef}
      hideOptions={viewType !== "trace"}
      enableScrollToBottom
      className={cn(
        "relative flex flex-1 flex-col overflow-y-auto px-4 py-3 @container min-w-[420px]",
        {
          "border-l": !isBase,
          "min-w-[600px]": viewType === "timeline",
        },
      )}
    >
      {span && (
        <div className="flex flex-1 flex-col gap-3.5 text-sm">
          <div className="flex h-7 items-center">
            <GridExperimentHeader
              dotClassName={comparisonClassName}
              experimentName={promptDisplayName}
              isBase={isBase}
              setColumnSort={() => {}}
              columns={[]}
              colIdx={colIdx}
              traceRowId={trace.root.id}
              runRef={runRef}
              context="playground-trace-header"
              isPending={isDelayedSpanChangeTransitioning}
            />
          </div>

          {trace.isPlaceholder && !isRunning ? (
            <TableEmptyState
              className="size-full justify-center"
              Icon={ListVideo}
              label="This prompt has not been run yet"
            />
          ) : viewType !== "trace" ? (
            isRunning ? (
              <SpanSkeleton />
            ) : (
              <NonTraceView
                trace={trace}
                comparisonClassName={comparisonClassName}
                scrollableContainerRef={scrollableContainerRef}
                copilotContext={copilotContext}
                startSpanChangeTransition={startSpanChangeTransition}
                objectType={OBJECT_TYPE}
                objectId={promptSessionId}
              />
            )
          ) : (
            <>
              <SpanName hasError={hasError} span={span} />
              <SpanContents
                isDatasetRow={false}
                isRoot={isRoot}
                span={span}
                hasError={hasError}
                trace={trace}
                projectConfig={projectConfig}
                onApplySearch={onApplySearch}
                autoScores={autoScores}
                copilotContext={copilotContext}
                resultIndex={resultIndex}
                searchResultFields={searchResultFields}
                searchQuery={searchQuery}
                objectType={OBJECT_TYPE}
                editableFields={EDITABLE_FIELDS}
                hiddenFields={
                  isRoot ? HIDDEN_ROOT_FIELDS : HIDDEN_NON_ROOT_FIELDS
                }
                defaultFieldOrder={DEFAULT_FIELD_ORDER}
                defaultCollapsedFields={DEFAULT_COLLAPSED_FIELDS}
                shouldRenderManualScores={false}
                comparisonClassName={comparisonClassName}
                localStorageKeyPrefix={localStorageKeyPrefix}
                shouldRenderSpanDetails
                diffMessage="Showing diff from comparison task -> base task"
              />
            </>
          )}
        </div>
      )}
    </ScrollableContainerWithOptions>
  );
}

const NonTraceView = ({
  trace,
  comparisonClassName,
  scrollableContainerRef,
  copilotContext,
  startSpanChangeTransition,
  objectType,
  objectId,
}: {
  trace: LoadedTrace;
  comparisonClassName: string;
  scrollableContainerRef: RefObject<HTMLDivElement | null>;
  copilotContext: MultiTraceContext;
  startSpanChangeTransition: TransitionStartFunction;
  objectType: DataObjectType;
  objectId: string | undefined;
}) => {
  const [collapseState, setCollapseState] = useState<traceCollapseState>({
    state: "expanded",
  });

  const flattenedRootData = flattenDiffObjects(trace?.root.data);
  const { traceStart, totalDuration } = getTraceDurations({
    flattenedRootData,
    calculateTotalDuration: true,
    rootChildren: trace?.spans ? Object.values(trace.spans) : undefined,
  });

  const { setSelectedSpan } = useSpanSelection({
    copilotContext,
    startSpanChangeTransition,
    trace,
    loadedTrace: trace,
  });

  return (
    <div className="-mx-3">
      <TraceTree
        trace={trace}
        seen={new Set()}
        comparisonClassName={comparisonClassName}
        selectedSpan={trace.root}
        setSelectedSpan={setSelectedSpan}
        showMetrics={true}
        collapseState={collapseState}
        onCollapseStateChange={setCollapseState}
        totalDuration={totalDuration}
        traceStart={traceStart}
        containerRef={scrollableContainerRef}
      />
    </div>
  );
};

function DiffSwitch({
  rowId,
  onToggle,
}: {
  rowId: RowId | null;
  onToggle?: () => void;
}) {
  const [diffMode, setDiffMode] = useDiffModeState();
  if (!rowId || !isDiffObject(rowId) || !getDiffLeft(rowId)) return null;

  return (
    <Button
      size="xs"
      variant="ghost"
      onClick={() => {
        onToggle?.();
        setDiffMode({
          enabled: !diffMode.enabled,
          enabledValue: "between_experiments",
        });
      }}
      asChild
      className="cursor-pointer select-none"
    >
      <div>
        Diff <Switch className="scale-90" checked={diffMode.enabled} />
      </div>
    </Button>
  );
}

function extractRunPromptInput(data: SpanData) {
  return {
    ...data,
    input: JSON.parse(data.input),
    expected: JSON.parse(data.expected),
    metadata: JSON.parse(data.metadata),
  };
}

function useTraceWithStreamingOutput({
  datasetRowId,
  generationId,
  placeholderTrace,
  playgroundLogTrace,
  showPlaceholder,
}: {
  datasetRowId: string;
  generationId?: string;
  placeholderTrace: LoadedTrace;
  playgroundLogTrace: LoadedTrace | undefined;
  showPlaceholder?: boolean;
}) {
  const setStreamingCompletion = useSetAtom(streamingCompletionsAtom);
  const atom = useStreamingCompletionsAtom(datasetRowId, generationId);
  const errorAtom = useStreamingCompletionErrorAtom(datasetRowId, generationId);
  const streamingValue = useAtomValue(atom);
  const error = useAtomValue(errorAtom);
  const hasStreamingValue = !!streamingValue || !!error;
  const _latestXactId = useAtomValue(latestXactIdAtom);

  const latestXactId = maxXactId([
    _latestXactId,
    placeholderTrace.root.data[TRANSACTION_ID_FIELD],
  ]);
  // Clear the streaming data once the playgroundLogRow has been updated
  // There are two cases here:
  // 1. The whole playground has been run, so dataset rows are replaced with playground_log rows as they come in.
  // 2. A single row has been re-run. The playgroundLogTrace is built from the same playground_log row, but is updated with new output.
  // We can handle both cases by clearing the streaming data once we have a playground_log row with output, a higher xact_id than
  // the one we saved when streaming began, and a truthy end time.
  useEffect(() => {
    if (
      hasStreamingValue &&
      playgroundLogTrace &&
      latestXactId &&
      playgroundLogTrace.root.data[TRANSACTION_ID_FIELD] > latestXactId &&
      playgroundLogTrace.root.data?.metrics?.end != null &&
      generationId
    ) {
      setStreamingCompletion((prev) => {
        const newState = { ...prev };
        if (newState[datasetRowId]) {
          const { [generationId]: _, ...rest } = newState[datasetRowId];
          newState[datasetRowId] = rest;
        }
        if (Object.keys(newState[datasetRowId] ?? {}).length === 0) {
          delete newState[datasetRowId];
        }
        return newState;
      });
    }
  }, [
    datasetRowId,
    generationId,
    playgroundLogTrace,
    hasStreamingValue,
    setStreamingCompletion,
    latestXactId,
  ]);

  return useMemo((): LoadedTrace => {
    const baseTrace =
      (showPlaceholder ? null : playgroundLogTrace) ?? placeholderTrace;
    if (streamingValue == null && error == null) {
      return baseTrace;
    }

    const traceRoot: Span = {
      ...baseTrace.root,
      data: {
        ...baseTrace.root.data,
        output: streamingValue,
        error,
        // @ts-ignore -- remove end to display span spinner sooner
        metrics: { ...baseTrace.root.data.metrics, end: undefined },
      },
    };

    const spans = Object.fromEntries(
      Object.keys(baseTrace).map((key) => [key, traceRoot]),
    );

    return {
      ...baseTrace,
      root: traceRoot,
      spans,
    };
  }, [
    showPlaceholder,
    placeholderTrace,
    playgroundLogTrace,
    streamingValue,
    error,
  ]);
}
