import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "#/ui/dialog";
import { Input } from "#/ui/input";
import { type PropsWithChildren, useEffect, useState, useMemo } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "#/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "#/ui/tabs";
import { Combobox } from "#/ui/combobox/combobox";
import { cn } from "#/utils/classnames";
import { useInferDatasetPaths } from "#/utils/custom-columns/use-infer-dataset-paths";
import { InfoBanner } from "#/ui/info-banner";

export const extraMessagesSchema = z.object({
  expression: z.string(),
});

export type ExtraMessagesFormValues = z.infer<typeof extraMessagesSchema>;

export type OnSaveExtraMessagesFn = (expression: string) => void;

type ExtraMessagesProps = PropsWithChildren<{
  setExtraMessages: OnSaveExtraMessagesFn;
  currentExpression?: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  datasetId?: string;
}>;

export const ExtraMessagesDialog = ({
  currentExpression = "",
  setExtraMessages,
  children,
  isOpen,
  setIsOpen,
  datasetId,
}: ExtraMessagesProps) => {
  const form = useForm<ExtraMessagesFormValues>({
    resolver: zodResolver(extraMessagesSchema),
    defaultValues: {
      expression: currentExpression,
    },
  });

  const datasetPaths = useInferDatasetPaths({ datasetId });

  useEffect(() => {
    form.reset({ expression: currentExpression });
  }, [currentExpression, form]);

  const onSubmit = async (data: ExtraMessagesFormValues) => {
    setExtraMessages(data.expression);
    setIsOpen(false);
  };

  const expr = form.watch("expression");

  const clearValue = () => {
    form.setValue("expression", "", {
      shouldDirty: true,
      shouldValidate: true,
    });
  };

  const comboboxOptions = useMemo(() => {
    const pathMap = new Map<string, { label: string; value: string }>();

    datasetPaths?.forEach((path) => {
      pathMap.set(path, {
        label: path,
        value: path,
      });
    });

    return Array.from(pathMap.values());
  }, [datasetPaths]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} modal={true}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="mb-2">
            Append messages from dataset
          </DialogTitle>
          <DialogDescription>
            Define a path from dataset rows to messages. If an array of messages
            is found at the specified path, the messages will be appended to the
            end of the prompt.
          </DialogDescription>
          <DialogDescription>
            An error will be thrown if the path does not include a valid array
            of messages.
          </DialogDescription>
        </DialogHeader>
        {!datasetId && (
          <InfoBanner>
            This feature is only available when a dataset is selected
          </InfoBanner>
        )}
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col"
          >
            <FormField
              control={form.control}
              name="expression"
              render={({ field }) => (
                <FormItem className="mb-4">
                  <FormControl>
                    {datasetId && comboboxOptions.length > 0 ? (
                      <Tabs defaultValue="inferred">
                        <TabsList className="mb-2 flex w-full items-end justify-start gap-4 rounded-none border-b pb-0 bg-transparent border-primary-100 dark:bg-transparent">
                          <TabsTrigger
                            value="inferred"
                            className="-mb-px rounded-none border-b p-0 pb-2 text-xs bg-transparent border-transparent data-[state=active]:bg-transparent data-[state=active]:border-primary-600"
                          >
                            Inferred paths
                          </TabsTrigger>
                          <TabsTrigger
                            value="manual"
                            className="-mb-px rounded-none border-b p-0 pb-2 text-xs bg-transparent border-transparent data-[state=active]:bg-transparent data-[state=active]:border-primary-600"
                          >
                            Manual
                          </TabsTrigger>
                        </TabsList>
                        <TabsContent
                          value="inferred"
                          className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                        >
                          <Combobox
                            contentWidth={462}
                            align="start"
                            variant="button"
                            buttonClassName="px-3 max-w-[462px] h-9 font-normal text-primary"
                            searchPlaceholder="Find field"
                            noResultsLabel="No fields found"
                            placeholderLabel="Select a field path"
                            placeholderClassName={cn(
                              "flex-1 text-left px-0 truncate",
                              {
                                "text-primary-500": !expr,
                              },
                            )}
                            selectedValue={expr}
                            onChange={(value) =>
                              value &&
                              form.setValue("expression", value, {
                                shouldValidate: true,
                                shouldDirty: true,
                              })
                            }
                            options={comboboxOptions}
                            modal
                          />
                        </TabsContent>
                        <TabsContent
                          value="manual"
                          className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                        >
                          <Input
                            autoFocus
                            placeholder="Enter dataset path to messages"
                            className="h-9 text-primary"
                            {...field}
                            ref={(el) => {
                              field.ref(el);
                              if (el) {
                                // the tab change steals focus, so we can't use autoFocus here.
                                // instead, we set a timeout to focus the input after the tab change.
                                setTimeout(() => {
                                  el.focus();
                                }, 100);
                              }
                            }}
                          />
                        </TabsContent>
                      </Tabs>
                    ) : (
                      <Input
                        autoFocus
                        placeholder="Enter dataset path to messages"
                        className="h-9 text-primary"
                        {...field}
                      />
                    )}
                  </FormControl>
                  <FormDescription className="flex justify-between">
                    If no path is provided, this feature will be disabled
                    {!!expr && (
                      <Button
                        transparent
                        size="inline"
                        className="text-xs text-accent-600"
                        onClick={clearValue}
                        type="button"
                      >
                        Clear
                      </Button>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="submit"
                isLoading={form.formState.isSubmitting}
                disabled={!form.formState.isValid || !form.formState.isDirty}
                size="sm"
                variant="primary"
              >
                Save
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export const ExtraMessages = ({
  currentExpression,
  setExtraMessages,
  children,
  datasetId,
}: {
  currentExpression?: string;
  setExtraMessages: OnSaveExtraMessagesFn;
  children: React.ReactNode;
  datasetId?: string;
}) => {
  const [isExtraMessagesOpen, setIsExtraMessagesOpen] = useState(false);

  return (
    <ExtraMessagesDialog
      currentExpression={currentExpression}
      setExtraMessages={setExtraMessages}
      isOpen={isExtraMessagesOpen}
      setIsOpen={setIsExtraMessagesOpen}
      datasetId={datasetId}
    >
      {children}
    </ExtraMessagesDialog>
  );
};
