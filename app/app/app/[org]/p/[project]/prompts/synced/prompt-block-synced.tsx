import { <PERSON><PERSON> } from "#/ui/button";
import PlainDropdown from "#/ui/plain-dropdown";
import { type TextEditorProps } from "#/ui/text-editor";
import {
  ChatPlaceholders,
  CompletionPlaceholders,
} from "#/utils/ai/placeholders";
import {
  type OpenAIModelParams,
  type MessageRole,
  type SavedFunctionId,
  type FunctionObjectType,
  type Message,
} from "@braintrust/core/typespecs";
import {
  type ModelSpec,
  modelProviderHasTools,
} from "@braintrust/proxy/schema";
import { memo, useCallback, useContext, useMemo, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  BetweenHorizontalStart,
  Clipboard,
  File,
  MessageSquare,
  Minus,
  Plus,
  PocketKnife,
  XIcon,
} from "lucide-react";
import { type TransactionId } from "#/utils/duckdb";
import { type PromptData } from "#/ui/prompts/schema";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { MessageActionButton } from "../message-action-button";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { Tools } from "../tools";
import { ProjectContext } from "../../projectContext";
import React from "react";
import { OutputFormat } from "../output-format";
import { type structuredOutputSchema } from "../structured-output";
import { toast } from "sonner";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { BraintrustStream, deserializePlainStringAsJSON } from "braintrust";
import { InfoBanner } from "#/ui/info-banner";
import MessageBlockSynced from "./message-block-synced";
import { MESSAGE_ROLES, useSyncedPrompts } from "./use-synced-prompts";
import { type z } from "zod";
import ToolCallBlockSynced from "./tool-call-block-synced";
import { FocusScope } from "@react-aria/focus";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import Link from "next/link";
import { invoke } from "#/app/app/[org]/prompt/[prompt]/scorers/invoke";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { useHotkeys } from "react-hotkeys-hook";
import { parse } from "#/utils/string";
import { BasicTooltip } from "#/ui/tooltip";
import { Spinner } from "#/ui/icons/spinner";

export type OnSaveToolFunctionsFn = (
  tools: SavedFunctionId[],
) => Promise<TransactionId | null>;

export type OnSaveResponseTypeFn = (
  type: "json_schema" | "json_object" | "text",
) => Promise<void>;

export type AgentPosition = "first" | "later";

export interface PromptBlockProps {
  isReadOnly?: boolean;
  agentPosition?: AgentPosition;
  data?: PromptData;
  model: string;
  allAvailableModels: { [name: string]: ModelSpec };
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  idx: number;
  extensions: TextEditorProps["extensions"];
  autoFocus?: boolean;
  disableTools?: boolean;
  copilotContext?: CopilotContextBuilder;
  isInPlayground?: boolean;
  datasetId?: string;
  promptId: string;
  type?: FunctionObjectType;
  rowData?: Record<string, unknown>;
  enableHotkeys?: boolean;
  extraMessagesPath?: string | null;
}

const PromptBlockSynced = memo(
  ({
    isReadOnly,
    agentPosition,
    data,
    model,
    allAvailableModels,
    triggerSave,
    runPrompts,
    extensions,
    disableTools,
    copilotContext,
    isInPlayground,
    datasetId,
    promptId,
    type,
    rowData,
    enableHotkeys,
    extraMessagesPath,
  }: PromptBlockProps) => {
    const { orgName } = useContext(ProjectContext);

    const {
      updateStructuredOutput,
      updateResponseType,
      updateTools,
      addMessage,
      removeMessage,
      updateMessageRole,
      addMessagePart,
      updateMessageToolId_NO_SAVE,
      toggleMessageToolCalls,
      focusedEditor,
    } = useSyncedPrompts();

    const onSaveStructuredOutput = useCallback(
      async (data: z.infer<typeof structuredOutputSchema>) => {
        await updateStructuredOutput({ id: promptId, data });
      },
      [promptId, updateStructuredOutput],
    );

    const onSaveResponseType = useCallback(
      async (type: "json_schema" | "json_object" | "text") => {
        await updateResponseType({ id: promptId, type });
      },
      [promptId, updateResponseType],
    );

    const {
      format,
      flavor,
      multimodal: isModelMultimodal,
    } = allAvailableModels[model] || {};

    const isMultimodalActive = useCallback(
      (role?: MessageRole) => !!isModelMultimodal && role === "user",
      [isModelMultimodal],
    );

    const canCallTools = useCallback(
      (role?: MessageRole) =>
        modelProviderHasTools[format] && role === "assistant",
      [format],
    );

    const validRoles = MESSAGE_ROLES[format];
    const messages =
      data?.prompt?.type === "chat"
        ? data.prompt.messages
        : [{ ...data?.prompt, role: undefined }];

    const debouncedSave = useDebouncedCallback(triggerSave, 500);

    const isChat = flavor === "chat";
    const supportsTools =
      isChat && modelProviderHasTools[format] && !data?.parser && !disableTools;
    const supportsOutputFormat = isChat && !data?.parser;
    const supportsStructuredOutput = supportsTools;

    // TODO: bring back variable detection
    const hasVariable = false;

    const [promptVariableTipDismissed, setPromptVariableTipDismissed] =
      useEntityStorage({
        entityType: "dismissableMessages",
        key: "promptVariableTipDismissed",
        entityIdentifier: orgName,
      });

    const rawToolsString =
      data?.prompt?.type === "chat" ? data?.prompt?.tools : null;
    const rawTools: Record<string, unknown>[] | null = useMemo(() => {
      if (!rawToolsString) return null;
      return deserializePlainStringAsJSON(rawToolsString).value ?? [];
    }, [rawToolsString]);

    const hasTools =
      (data?.tool_functions?.length ?? 0) > 0 || (rawTools?.length ?? 0) > 0;

    const variableTipText = (
      <>
        Try inserting dataset variables from{" "}
        <code className="font-semibold text-comparison-700">{`{{input}}`}</code>
        ,{" "}
        <code className="font-semibold text-comparison-700">{`{{expected}}`}</code>
        , or{" "}
        <code className="font-semibold text-comparison-700">{`{{metadata}}`}</code>
      </>
    );

    const org = useOrg();

    const { getOrRefreshToken } = useSessionToken();

    const [previewText, setPreviewText] = useState("");

    const runData = useMemo(() => {
      if (!rowData) return null;

      return {
        input: parse(rowData?.input),
        expected: parse(rowData?.expected),
        metadata: parse(rowData?.metadata),
      };
    }, [rowData]);

    const [isGenerating, setIsGenerating] = useState(false);
    const onRun = useCallback(async () => {
      if (!data) {
        toast.error("Prompt not loaded");
        return;
      }

      setIsGenerating(true);
      let result: Response | null = null;
      try {
        result = await invoke({
          orgName: org.name,
          sessionToken: await getOrRefreshToken(),
          proxyUrl: org.proxy_url,
          functionId: {
            inline_prompt: data,
          },
          input: runData?.input,
          expected: runData?.expected,
          metadata: runData?.metadata,
          stream: true,
        });
      } catch (e) {
        console.error(e);
        toast.error("Failed to run prompt", {
          description: e instanceof Error ? e.message : String(e),
        });
        setIsGenerating(false);

        return;
      }

      if (!result) {
        console.warn("No result");
        setIsGenerating(false);
        return;
      }
      const body = result.body;
      if (!body) {
        toast.error("No response body");
        setIsGenerating(false);
        return;
      }

      await processResultStream({
        stream: body,
        setPreviewText,
        addNewMessage: (message) => {
          setPreviewText("");
          addMessage({ id: promptId, content: message.content });
        },
      });
      setIsGenerating(false);
    }, [
      data,
      getOrRefreshToken,
      org.name,
      org.proxy_url,
      runData,
      addMessage,
      promptId,
    ]);

    useHotkeys(
      "M",
      () => {
        addMessage({ id: promptId });
      },
      {
        enabled: enableHotkeys,
        preventDefault: true,
      },
    );

    return (
      <FocusScope>
        <div className="flex flex-auto grow flex-col">
          {messages.map((message, index) => (
            <MessageContainer
              key={`${index}${focusedEditor.current?.messageIndex === index && focusedEditor.current?.focusKey ? `-${focusedEditor.current?.focusKey}` : ""}`}
              isReadOnly={isReadOnly}
              flavor={flavor}
              isChat={isChat}
              validRoles={validRoles}
              role={message?.role}
              actionsSlot={
                !isReadOnly && (
                  <div className="-mr-1 flex gap-1 opacity-0 transition-opacity group-hover/prompt:opacity-100">
                    <MessageActionButton
                      onClick={() => {
                        if (!message?.content) return;
                        if (typeof message.content === "string") {
                          navigator.clipboard.writeText(message.content);
                          toast.success("Message copied to clipboard");
                          return;
                        }
                        if (Array.isArray(message.content)) {
                          const textPart = message.content.find(
                            (part) => part.type === "text",
                          );
                          if (textPart && "text" in textPart) {
                            navigator.clipboard.writeText(textPart.text);
                            toast.success("Message copied to clipboard");
                          }
                        }
                      }}
                      tooltip="Copy message to clipboard"
                      Icon={Clipboard}
                    />
                    {canCallTools(message.role) && (
                      <MessageActionButton
                        onClick={() =>
                          toggleMessageToolCalls({
                            id: promptId,
                            index,
                          })
                        }
                        tooltip="Toggle tool calls"
                        Icon={PocketKnife}
                      />
                    )}

                    <MessageActionButton
                      onClick={() => {
                        addMessage({ id: promptId, afterIndex: index });
                      }}
                      tooltip="Add message"
                      Icon={Plus}
                    />
                    {messages.length > 1 && (
                      <MessageActionButton
                        onClick={() => {
                          removeMessage({ id: promptId, index });
                        }}
                        tooltip="Remove message"
                        Icon={Minus}
                      />
                    )}
                  </div>
                )
              }
              onRoleChange={(role) =>
                updateMessageRole({
                  id: promptId,
                  index,
                  newRole: role,
                  newIsMultimodal: isMultimodalActive(role),
                })
              }
            >
              {message?.role === "tool" && (
                <div className="mb-2">
                  <PlainInput
                    value={message.tool_call_id}
                    onChange={(e) => {
                      updateMessageToolId_NO_SAVE({
                        id: promptId,
                        index,
                        toolId: e.target.value,
                      });
                      debouncedSave();
                    }}
                    disabled={isReadOnly}
                    required
                    placeholder="Enter tool call ID"
                    className="-ml-2 block h-8 w-full max-w-xs px-2 font-mono text-xs placeholder:font-inter"
                  />
                </div>
              )}
              <MessageBlockSynced
                isReadOnly={isReadOnly}
                data={message?.content}
                isMultimodal={isMultimodalActive(message.role)}
                triggerSave={triggerSave}
                runPrompts={runPrompts}
                extensions={extensions}
                textPlaceholder={
                  flavor === "completion"
                    ? CompletionPlaceholders[format]
                    : message?.role &&
                      ChatPlaceholders[format] &&
                      ChatPlaceholders[format][message.role]
                }
                copilotContext={copilotContext}
                promptId={promptId}
                messageIdx={index}
              />
              {!isReadOnly && isChat && isMultimodalActive(message.role) && (
                <div className="flex gap-1 pt-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        className="text-primary-700"
                        Icon={Plus}
                        size="xs"
                        isDropdown
                      >
                        Message part
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem
                        onSelect={() =>
                          // This is a hack to ensure the new message part is added and auto-focused _after_ radix refocuses the dropdown trigger
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "text",
                              }),
                            50,
                          )
                        }
                      >
                        <MessageSquare className="size-3" /> Text
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() =>
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "image_url",
                              }),
                            50,
                          )
                        }
                      >
                        <File className="size-3" /> File
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
              {message?.role === "assistant" && message?.tool_calls && (
                <ToolCallBlockSynced
                  value={message.tool_calls}
                  triggerSave={triggerSave}
                  runPrompts={runPrompts}
                  promptId={promptId}
                  messageIdx={index}
                  // TODO: bring back focus management
                  // autoFocus={autoFocus || focusedEditor === i}
                />
              )}
            </MessageContainer>
          ))}
          {previewText && (
            <MessageContainer
              isReadOnly
              flavor={flavor}
              isChat={isChat}
              validRoles={validRoles}
              role="assistant"
            >
              <div className="text-sm text-primary-500">{previewText}</div>
            </MessageContainer>
          )}
          {isChat && (
            <>
              {!promptVariableTipDismissed && isInPlayground && !isReadOnly && (
                <div className="mb-2 flex items-center gap-2 rounded-md border pl-3 pr-0 text-xs bg-primary-100/80 border-primary-200/50 text-primary-500">
                  <div className="flex-1 py-2 leading-normal">
                    {variableTipText}
                  </div>
                  <Button
                    size="icon"
                    transparent
                    className="size-8 flex-none text-primary-400"
                    Icon={XIcon}
                    onClick={() => {
                      setPromptVariableTipDismissed(true);
                    }}
                  />
                </div>
              )}
              {agentPosition === "first" && !isReadOnly && (
                <InfoBanner>{variableTipText}</InfoBanner>
              )}
              {agentPosition === "later" && !isReadOnly && (
                <InfoBanner>
                  Learn about{" "}
                  <Link
                    href="/docs/guides/functions/agents#variables"
                    target="_blank"
                    className="font-medium hover:underline"
                  >
                    variable interpolation
                  </Link>{" "}
                  in agents.
                </InfoBanner>
              )}
              {hasVariable &&
                !datasetId &&
                agentPosition !== "later" &&
                !isReadOnly && (
                  <div className="mb-2 rounded-md border p-3 text-xs bg-primary-100/80 border-primary-200/50 text-primary-500">
                    This prompt includes variables, but there is no dataset
                    selected.
                  </div>
                )}
              {extraMessagesPath && (
                <div className="mb-2 flex flex-none items-center gap-2 rounded-md border border-dashed p-3 text-xs border-primary-200 text-primary-500">
                  <BetweenHorizontalStart className="size-3 flex-none" />
                  <span>
                    Messages from the dataset path{" "}
                    <code className="font-semibold text-comparison-700">
                      {extraMessagesPath}
                    </code>{" "}
                    will be appended to this prompt
                  </span>
                </div>
              )}
              <div className="mb-2 flex w-full flex-wrap gap-2">
                {!isReadOnly && (
                  <Button
                    size="xs"
                    onClick={(e) => {
                      e.preventDefault();
                      addMessage({ id: promptId });
                    }}
                    className="flex-none"
                    Icon={Plus}
                  >
                    Message
                  </Button>
                )}
                {supportsTools && (
                  <Tools
                    selectedTools={data?.tool_functions}
                    onSave={async (tools, rawTools, toolChoice) =>
                      await updateTools({
                        id: promptId,
                        toolFunctions: tools ?? [],
                        rawTools,
                        toolChoice,
                      })
                    }
                    copilotContext={copilotContext}
                    rawToolsValue={rawTools}
                    toolChoice={
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                      data?.options?.params
                        ?.tool_choice as OpenAIModelParams["tool_choice"]
                    }
                    isReadOnly={isReadOnly}
                    type={type}
                  />
                )}
                {supportsOutputFormat && (
                  <OutputFormat
                    onSaveStructuredOutput={onSaveStructuredOutput}
                    currentInitData={data}
                    supportsStructuredOutput={supportsStructuredOutput}
                    onSaveResponseType={onSaveResponseType}
                    isReadOnly={isReadOnly}
                  />
                )}
                {!isReadOnly &&
                  !isGenerating &&
                  messages[messages.length - 1]?.role === "user" && (
                    <BasicTooltip
                      tooltipContent={
                        hasTools
                          ? "Currently, generate message is not available for prompts that include tools"
                          : null
                      }
                    >
                      <Button
                        disabled={messages.length === 0 || hasTools}
                        size="xs"
                        Icon={Plus}
                        onClick={onRun}
                        variant="ghost"
                        className="flex-none text-primary-600"
                      >
                        Generate message
                      </Button>
                    </BasicTooltip>
                  )}
                {isGenerating && (
                  <div className="flex w-fit items-center">
                    <Spinner className="my-auto size-3 text-primary-500" />
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </FocusScope>
    );
  },
);

const MessageContainer = ({
  isReadOnly,
  flavor,
  isChat,
  children,
  actionsSlot,
  onRoleChange,
  validRoles,
  role,
}: {
  isReadOnly?: boolean;
  flavor: "chat" | "completion";
  isChat: boolean;
  children: React.ReactNode;
  actionsSlot?: React.ReactNode;
  onRoleChange?: (role: MessageRole) => void;
  validRoles: MessageRole[];
  role?: MessageRole;
}) => {
  return (
    <div
      className={cn(
        `group/prompt flex flex-col mb-2 pb-3 pt-1 bg-transparent border border-primary-200/50 focus-within:border-primary-200 hover:bg-primary-200/20 focus-within:hover:bg-primary-200/60 focus-within:bg-primary-200/60 transition-colors rounded-md px-3 min-h-[2.5lh] cursor-text`,
        {
          "bg-transparent": isReadOnly,
          grow: flavor === "completion",
        },
      )}
    >
      {isChat && (
        <div className="mb-1 flex h-7 items-center">
          <div className="flex-auto text-sm font-medium capitalize">
            <PlainDropdown
              selectedOption={role}
              setSelectedOption={(role) => onRoleChange?.(role)}
              className="-ml-1.5 rounded-md px-1.5 py-1 text-xs transition-all text-primary-700 hover:bg-primary-200"
              disabled={isReadOnly}
              options={validRoles}
              itemClassName="capitalize"
            />
          </div>
          {actionsSlot}
        </div>
      )}

      {children}
    </div>
  );
};

PromptBlockSynced.displayName = "PromptBlockSynced";

export default PromptBlockSynced;

async function processResultStream({
  stream: rawStream,
  setPreviewText,
  addNewMessage,
}: {
  stream: ReadableStream<Uint8Array>;
  setPreviewText: (text: string) => void;
  addNewMessage: (message: Message) => void;
}) {
  const textChunks: string[] = [];
  const jsonChunks: string[] = [];
  const errors: string[] = [];

  let previewText = "";

  const stream = new BraintrustStream(rawStream);
  const reader = stream.toReadableStream().getReader();
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    switch (value.type) {
      case "text_delta":
        textChunks.push(value.data);
        previewText += value.data;
        break;
      case "json_delta":
        jsonChunks.push(value.data);
        previewText += value.data;
        break;
      case "error":
        errors.push("ERROR! " + value.data);
        break;
      case "done":
        break;
      default:
        console.warn("Unknown stream type", value);
    }
    setPreviewText(previewText);
  }

  // NOTE: This fundamentally does not work for tool calls right now, because we don't have a mode where we always
  // propagate tool call ids. We should either call the proxy directly (and get an LLM response) or add a flag/mode that
  // includes tool call ids.
  addNewMessage({
    role: "assistant",
    content: textChunks.length > 0 ? textChunks.join("") : jsonChunks.join(""),
  });
}
