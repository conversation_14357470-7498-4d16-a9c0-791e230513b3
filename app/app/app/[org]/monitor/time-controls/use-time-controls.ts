import { useQueryState, parseAsString, parseAsStringLiteral } from "nuqs";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  type TimeSpan,
  timeFrameFromStrings,
  type TimeRange,
  timeSpanToTimeFrame,
  selectGranularityBucket,
  type TimeFrame,
  TIME_RANGE_OPTIONS,
  DEFAULT_TIME_RANGE,
  type ChartTimeFrame,
  timeFrameToChartTimeFrame,
  TIME_RANGE_TO_MILLISECONDS,
  getNextLETimeRange,
} from "./time-range";
import { type View } from "#/utils/view/use-view-generic";
import { getLiveRefreshInterval } from "./get-live-refresh-interval";
import { useQuery } from "@tanstack/react-query";

const timeRangeValues = TIME_RANGE_OPTIONS.map((r) => r.value);

// hack to avoid requeries when new chart timeframe is close
const updateIfNotClose = (n: ChartTimeFrame) => {
  return (prev: ChartTimeFrame) => {
    const dS = Math.abs(n.start - prev.start);
    const dE = Math.abs(n.end - prev.end);

    // new reference
    if (dS > 200 || dE > 200) {
      return n;
    }

    // else do not update
    return prev;
  };
};

export const useTimeControls = () => {
  const [isLive, setIsLive] = useState<boolean>(true);

  const [spanType, setSpanType] = useQueryState(
    "spanType",
    parseAsStringLiteral(["range", "frame"]).withDefault("range"),
  );
  const [rangeValue, setRangeValue] = useQueryState(
    "rangeValue",
    parseAsStringLiteral(timeRangeValues).withDefault(DEFAULT_TIME_RANGE.value),
  );
  const [frameStart, setFrameStart] = useQueryState(
    "frameStart",
    parseAsString.withDefault(""),
  );
  const [frameStop, setFrameStop] = useQueryState(
    "frameStop",
    parseAsString.withDefault(""),
  );

  const timeSpan: TimeSpan = useMemo(() => {
    if (spanType === "frame" && frameStart && frameStop) {
      return timeFrameFromStrings(frameStart, frameStop);
    }

    if (
      spanType === "range" &&
      typeof rangeValue === "string" &&
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      timeRangeValues.includes(rangeValue as TimeRange)
    ) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return rangeValue as TimeRange;
    }

    // url is not valid, use default
    return DEFAULT_TIME_RANGE.value;
  }, [spanType, frameStart, frameStop, rangeValue]);

  const [chartTimeFrame, setChartTimeFrame] = useState<ChartTimeFrame>(
    timeFrameToChartTimeFrame(timeSpanToTimeFrame(timeSpan)),
  );

  const getChartTimeFrame = useCallback(() => {
    if (typeof timeSpan !== "string") {
      // explicit timeframe case
      return {
        start: timeSpan.start,
        end: timeSpan.end ?? new Date().getTime(),
      };
    }

    // live timeframe case
    let sanitizedRange = rangeValue;
    if (!timeRangeValues.includes(rangeValue)) {
      sanitizedRange = DEFAULT_TIME_RANGE.value;
    }

    const timeframe = timeSpanToTimeFrame(sanitizedRange);
    return timeFrameToChartTimeFrame(timeframe);
  }, [rangeValue, timeSpan]);

  const liveRefreshInterval = useMemo(() => {
    const duration =
      TIME_RANGE_TO_MILLISECONDS[rangeValue || DEFAULT_TIME_RANGE.value];
    const interval = getLiveRefreshInterval(duration);
    return interval;
  }, [rangeValue]);

  // use a dummy useQuery for polling updates and auto disable updates when page in background
  const { data: queryChartTimeFrame } = useQuery({
    queryKey: ["monitor-page", timeSpan, rangeValue],
    queryFn: getChartTimeFrame,
    // stale at refresh interval, else one hour if not live
    staleTime: isLive ? liveRefreshInterval : 3600_000,
    // poll refresh if live, else do not
    refetchInterval: isLive ? liveRefreshInterval : Infinity,
  });

  // update local chartTimeFrame when it changes from useQuery
  useEffect(() => {
    if (queryChartTimeFrame) {
      setChartTimeFrame(queryChartTimeFrame);
    }
  }, [queryChartTimeFrame]);

  const play = useCallback(
    (timeRange: TimeRange) => {
      setSpanType("range");
      setRangeValue(timeRange);
      setFrameStart(null);
      setFrameStop(null);
      setIsLive(true);
    },
    [setSpanType, setRangeValue, setFrameStart, setFrameStop, setIsLive],
  );

  const pause = useCallback(
    (tf: ChartTimeFrame) => {
      setSpanType("frame");
      setRangeValue(null);
      setFrameStart(String(Math.round(tf.start)));
      setFrameStop(String(Math.round(tf.end)));
      setIsLive(false);
      setChartTimeFrame(updateIfNotClose(tf));
    },
    [
      setSpanType,
      setRangeValue,
      setFrameStart,
      setFrameStop,
      setIsLive,
      setChartTimeFrame,
    ],
  );

  const { start, end } = useMemo(
    () => timeSpanToTimeFrame(timeSpan),
    [timeSpan],
  );

  const timeBucket: "minute" | "hour" | "day" = useMemo(() => {
    return selectGranularityBucket(chartTimeFrame);
  }, [chartTimeFrame]);

  const timeFrame: TimeFrame = useMemo(
    () => ({
      start,
      end,
    }),
    [start, end],
  );

  const startTime = useMemo(() => new Date(start).toISOString(), [start]);

  const setExplicitTimeFrame = useCallback(
    (start: number, end: number) => {
      pause({ start, end });
    },
    [pause],
  );

  const setTimeSpan = useCallback(
    (timeSpan: TimeSpan) => {
      if (typeof timeSpan === "string") {
        play(timeSpan);
      } else if (timeSpan.start && timeSpan.end) {
        pause({
          start: timeSpan.start,
          end: timeSpan.end ?? new Date().getTime(),
        });
      }
    },
    [pause, play],
  );

  const loadMonitorView = useCallback(
    (view: View) => {
      const viewOptions = view.options;
      if (
        !viewOptions ||
        !("viewType" in viewOptions && viewOptions.viewType === "monitor")
      ) {
        return;
      }

      const spanType = viewOptions.options?.spanType ?? "range";
      if (spanType === "frame") {
        const tf = timeFrameFromStrings(
          viewOptions.options?.frameStart ?? "",
          viewOptions.options?.frameEnd ?? "",
        );
        pause({
          start: tf.start,
          end: tf.end ?? new Date().getTime(),
        });
      } else if (spanType === "range") {
        play(viewOptions.options?.rangeValue ?? DEFAULT_TIME_RANGE.value);
      }
    },
    [pause, play],
  );

  // use the current chart timeframe on an external pause
  const externalPause = useCallback(() => {
    pause(chartTimeFrame);
  }, [pause, chartTimeFrame]);

  // use current range or default on an external play
  const externalPlay = useCallback(() => {
    const duration = chartTimeFrame.end - chartTimeFrame.start;
    const nextLarger = getNextLETimeRange(duration);
    play(nextLarger);
  }, [play, chartTimeFrame]);

  return {
    rangeValue,
    frameStart,
    frameStop,
    spanType,
    timeBucket,
    timeSpan,
    timeFrame,
    chartTimeFrame,
    startTime,
    setExplicitTimeFrame,
    loadMonitorView,
    setTimeSpan,
    isLive: isLive,
    pause: externalPause,
    play: externalPlay,
  };
};
