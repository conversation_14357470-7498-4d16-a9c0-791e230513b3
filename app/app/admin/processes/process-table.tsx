"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { Field, Schema, Utf8 } from "apache-arrow";
import { Table } from "#/ui/arrow-table";
import {
  endpointSchemas,
  type LastIndexOperationWithObjectId,
} from "@braintrust/local/app-schema";
import { useViewStates } from "#/utils/view/use-view";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { ExternalLinkIcon } from "lucide-react";
import { smartTimeFormat } from "#/ui/date";
import { type ObjectLookup, type adminFindObject } from "../actions";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { makeFullObjectLink } from "#/ui/layout/admin-object-finder";
import { StatusCell } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import { Spinner } from "#/ui/icons/spinner";
import { ExternalLink } from "#/ui/link";

const objectSchema = new Schema([
  Field.new({ name: "segment_id", type: new Utf8() }),
  Field.new({ name: "object_id", type: new Utf8() }),
  Field.new({ name: "start", type: new Utf8() }),
  Field.new({ name: "last_updated", type: new Utf8() }),
  Field.new({ name: "status", type: new Utf8() }),
  Field.new({ name: "parent", type: new Utf8() }),
]);

export function ProcessTable({
  indexOperations,
  scrollContainerRef,
}: {
  indexOperations: LastIndexOperationWithObjectId[] | null;
  scrollContainerRef?: React.RefObject<HTMLDivElement | null>;
}) {
  const withId = useMemo(() => {
    return indexOperations?.map((x) => ({ ...x, id: x.segment_id })) ?? null;
  }, [indexOperations]);

  const viewProps = useViewStates({
    pageIdentifier: "admin-processes",
    viewParams: undefined,
    clauseChecker: null,
  });

  const formatters = useMemo(() => {
    return makeFormatterMap<
      LastIndexOperationWithObjectId & {
        id: string;
        status?: undefined;
      },
      string
    >({
      start: {
        cell: (props) => {
          return (
            props.value && (
              <span>{smartTimeFormat(new Date(props.value).getTime())}</span>
            )
          );
        },
      },
      last_updated: {
        cell: (props) => {
          return (
            props.value && (
              <span>{smartTimeFormat(new Date(props.value).getTime())}</span>
            )
          );
        },
      },
      object: {
        cell: (props) => {
          return <span>{props.value}</span>;
        },
      },
      parent: {
        cell: (props) => {
          return <ObjectCell row={props.row.original} />;
        },
      },
      status: {
        cell: (props) => {
          return <StatusCell status={props.row.original} />;
        },
      },
    });
  }, []);

  return (
    <Table
      data={withId}
      fields={objectSchema.fields}
      viewProps={viewProps}
      scrollContainerRef={scrollContainerRef}
      formatters={formatters}
      tableType="list"
    />
  );
}

const objectResponseSchema = endpointSchemas.self_get_object_info.output;
export function ObjectCell({ row }: { row: LastIndexOperationWithObjectId }) {
  const [adminObjectLookup, setAdminObjectLookup] =
    useState<ObjectLookup | null>(null);
  const [myObjectLookup, setMyObjectLookup] = useState<{
    orgName: string;
    projectName: string;
    objectType: string;
    objectId: string;
  } | null>(null);
  const [objectLookupError, setObjectLookupError] = useState<string | null>(
    null,
  );

  const { getToken } = useAuth();
  const findAdminObject = useCallback(
    async (objectId: string) =>
      await invokeServerAction<typeof adminFindObject>({
        fName: "adminFindObject",
        args: {
          objectId,
        },
        getToken,
      }),
    [getToken],
  );

  const findMyObject = useCallback(async (objectId: string) => {
    const [objectType, id] = objectId.split(":");
    const { aclObjectType, overrideRestrictObjectType } =
      objectTypeToAclObjectType(objectType);
    const resp = await fetch("/api/self/get_object_info", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        object_type: aclObjectType,
        object_ids: [id],
        override_restrict_object_type: overrideRestrictObjectType,
      }),
    });
    const allObjects = objectResponseSchema.parse(await resp.json());
    if (allObjects.length === 0) {
      throw new Error("Object not found");
    }

    const o = allObjects[0];

    return {
      orgName: o.parent_cols.organization.name,
      projectName: o.parent_cols.project.name,
      objectType: objectType,
      objectId: o.object_id,
    };
  }, []);

  useEffect(() => {
    findAdminObject(row.object_id)
      .then((o) => {
        if (o.type === "object") {
          setAdminObjectLookup(o);
        } else {
          throw new Error("Object not found");
        }
      })
      .catch(() =>
        findMyObject(row.object_id)
          .then((o) => {
            setMyObjectLookup(o);
          })
          .catch((e) => {
            setObjectLookupError(e.message);
          }),
      );
  }, [row.object_id, findAdminObject, findMyObject]);

  if (objectLookupError) {
    return (
      <div className="mb-2 flex flex-none items-center gap-1.5 rounded-md border p-2 font-mono text-xs font-semibold bg-rose-500/5 border-rose-500/10 text-bad-700">
        {`${objectLookupError}`}
      </div>
    );
  } else if (adminObjectLookup) {
    return (
      <span>
        <ExternalLink
          href={makeFullObjectLink(adminObjectLookup.object)}
          className="inline-flex items-center gap-1 text-blue-500"
          target="_blank"
        >
          {adminObjectLookup.object.org_name} {"//"}{" "}
          {adminObjectLookup.object.project_name}
          <ExternalLinkIcon className="size-3" />
        </ExternalLink>
      </span>
    );
  } else if (myObjectLookup) {
    return (
      <span>
        <ExternalLink
          href={`/app/${encodeURIComponent(myObjectLookup.orgName)}/object?object_type=${encodeURIComponent(myObjectLookup.objectType)}&object_id=${encodeURIComponent(myObjectLookup.objectId)}`}
          className="inline-flex items-center gap-1 text-blue-500"
          target="_blank"
        >
          {myObjectLookup.orgName} {"//"} {myObjectLookup.projectName}
          <ExternalLinkIcon className="size-3" />
        </ExternalLink>
      </span>
    );
  } else {
    return <Spinner />;
  }
}

// This is copied from objectTypeToAclObjectType in api-ts/src/util.ts and is just an approximation
// that is hopefully good enough for admin lookups
export function objectTypeToAclObjectType(objectType: string): {
  aclObjectType: string;
  overrideRestrictObjectType?: string;
} {
  switch (objectType) {
    case "experiment":
      return { aclObjectType: "experiment" };
    case "dataset":
      return { aclObjectType: "dataset" };
    case "prompt_session":
    case "playground_logs":
      return { aclObjectType: "prompt_session" };
    case "project":
      return { aclObjectType: "project" };
    case "project_prompts":
    case "project_functions":
      return { aclObjectType: "project", overrideRestrictObjectType: "prompt" };
    case "project_logs":
      return { aclObjectType: "project_log" };
    case "org_prompts":
    case "org_functions":
      return {
        aclObjectType: "org_project",
        overrideRestrictObjectType: "prompt",
      };
    case "org_project_metadata":
      return {
        aclObjectType: "org_project",
      };
    default:
      throw new Error(`Unknown object type: ${objectType}`);
  }
}
