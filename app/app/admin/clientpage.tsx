"use client";

import { type FormatterProps, Table } from "#/ui/arrow-table";
import { useViewStates } from "#/utils/view/use-view";
import { <PERSON><PERSON>, Utf8, Int64 } from "apache-arrow";
import { Schema } from "apache-arrow";
import { Field } from "apache-arrow";
import { type MouseEventHand<PERSON>, useMemo, useRef, useState } from "react";
import { PlainInput } from "#/ui/plain-input";
import { Search } from "lucide-react";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import Fuse from "fuse.js";
import { useIsClient } from "#/utils/use-is-client";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import { useRouter } from "next/navigation";
import { type Row } from "@tanstack/react-table";
import { type FormatterMap } from "#/ui/field-to-column";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { type BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { type adminFetchAllOrgs, type OrgRow } from "./actions";
import { useQueryFunc } from "#/utils/react-query";
import { getLabelForPlanId } from "../app/[org]/settings/billing/plans";
import {
  FREE_TIER_LOG_BYTES_LIMIT,
  ONE_GIB,
  ONE_KIB,
  ONE_MIB,
} from "./org/[org]/resources";

const orgSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "self_hosted", type: new Bool() }),
  Field.new({ name: "span_limit", type: new Int64() }),
  Field.new({ name: "log_bytes_limit", type: new Int64() }),
  Field.new({ name: "telemetry_url", type: new Utf8() }),
  Field.new({ name: "plan_id", type: new Utf8() }),
]);

export function makeOrgLink(name: string) {
  return `/admin/org/${encodeURIComponent(name)}`;
}

const keyValuePattern = /(\w+):\s*([^\s]+)/gi;

function getFilterFunction(
  key: string,
): ((value: string) => (org: OrgRow) => boolean) | undefined {
  switch (key.toLowerCase()) {
    case "span_limit":
      return (value: string) => (org: OrgRow) => {
        const lowerValue = value.toLowerCase();
        if (lowerValue === "unlimited" && org.span_limit === null) {
          return true;
        }
        if (
          lowerValue.includes("free") &&
          (org.span_limit === 1000 || org.span_limit === 250_000)
        ) {
          return true;
        }
        // Match number+k|m anywhere in the string, e.g. "100k"
        const numericMatch = lowerValue.match(/(\d+)([kM])?/i);
        if (numericMatch) {
          const number = parseInt(numericMatch[1]);
          const suffix = numericMatch[2]?.toLowerCase();

          let multiplier = 1;
          if (suffix === "k") {
            multiplier = 1000;
          } else if (suffix === "m") {
            multiplier = 1000000;
          }

          if (org.span_limit === number * multiplier) {
            return true;
          }
        }
        return false;
      };
    case "log_bytes_limit":
      return (value: string) => (org: OrgRow) => {
        const lowerValue = value.toLowerCase();
        if (lowerValue === "unlimited" && org.log_bytes_limit === null) {
          return true;
        }
        if (
          lowerValue.includes("free") &&
          org.log_bytes_limit === 250_000_000
        ) {
          return true;
        }
        // Match number+k|m anywhere in the string, e.g. "100k"
        const numericMatch = lowerValue.match(/(\d+)([kM])?/i);
        if (numericMatch) {
          const number = parseInt(numericMatch[1]);
          const suffix = numericMatch[2]?.toLowerCase();

          let multiplier = 1;
          if (suffix === "k") {
            multiplier = 1000;
          } else if (suffix === "m") {
            multiplier = 1000000;
          }

          if (org.log_bytes_limit === number * multiplier) {
            return true;
          }
        }
        return false;
      };

    case "self_hosted":
      return (value: string) => (org: OrgRow) => {
        const boolValue = value === "true" || value === "yes";
        return (org.api_url !== null && org.api_url !== "") === boolValue;
      };

    case "plan_id":
      return (value: string) => (org: OrgRow) => {
        const label = getLabelForPlanId(org.plan_id ?? "");
        if (label) {
          return label.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      };

    default:
      return undefined;
  }
}

export function ClientPage({ orgRows: orgRowsServer }: { orgRows: OrgRow[] }) {
  const { data: orgRows } = useQueryFunc<typeof adminFetchAllOrgs>({
    fName: "adminFetchAllOrgs",
    args: {},
    serverData: orgRowsServer,
  });

  const [search, setSearch] = useState("");
  const viewProps = useViewStates({
    pageIdentifier: "admin-orgs",
    viewParams: undefined,
    clauseChecker: null,
  });

  const isClient = useIsClient();

  const setSearchDebounced = useDebouncedCallback(setSearch, 200);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const fuse = useMemo(
    () =>
      new Fuse(orgRows, {
        keys: ["name", "id"],
        threshold: 0.0,
        ignoreLocation: true,
        includeMatches: true,
      }),
    [orgRows],
  );

  const extractKeyValuePairs = (searchText: string) => {
    let match;
    let remainingText = searchText;

    const matches: { fullMatch: string; key: string; value: string }[] = [];
    while ((match = keyValuePattern.exec(searchText)) !== null) {
      matches.push({
        key: match[1].toLowerCase(),
        value: match[2].trim().toLowerCase(),
        fullMatch: match[0],
      });
    }

    // Remove all matches from the text to get the remaining text
    remainingText = searchText;
    matches.forEach((match) => {
      remainingText = remainingText.replace(match.fullMatch, "");
    });

    return { pairs: matches, remainingText: remainingText.trim() };
  };

  const filteredOrgs = useMemo(() => {
    if (!search) return orgRows;

    const { pairs, remainingText } = extractKeyValuePairs(search);

    let filteredData = orgRows;

    pairs.forEach((pair) => {
      const filterFunction = getFilterFunction(pair.key);
      if (filterFunction) {
        const filter = filterFunction(pair.value);
        filteredData = filteredData.filter(filter);
      }
    });

    if (remainingText) {
      const results = fuse.search(remainingText);
      filteredData = results.map((result) => result.item);
    }

    return filteredData;
  }, [search, fuse, orgRows]);

  const projectedOrgs = useMemo(
    () =>
      filteredOrgs.map((org) => ({
        ...org,
        self_hosted: org.api_url !== null && org.api_url !== "",
        telemetry_url: org.telemetry_url,
        plan_id: org.plan_id,
      })),
    [filteredOrgs],
  );

  const router = useRouter();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const rowEvents: Record<string, (row: Row<any>) => MouseEventHandler> =
    useMemo(() => {
      return {
        onClick: (row) => (e) => {
          if (e.ctrlKey || e.metaKey || e.button === 1) {
            window.open(makeOrgLink(row.original.name), "_blank");
          } else {
            router.push(makeOrgLink(row.original.name));
          }
        },
        onAuxClick: (row) => (e) => {
          window.open(makeOrgLink(row.original.name), "_blank");
        },
      };
    }, [router]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formatters: FormatterMap<OrgRow, any> = useMemo(
    () =>
      makeFormatterMap({
        span_limit: {
          cell: SpanLimitFormatter,
        },
        log_bytes_limit: {
          cell: LogBytesLimitFormatter,
        },
        telemetry_url: {
          cell: TelemetryUrlFormatter,
        },
        plan_id: {
          cell: PlanIdFormatter,
        },
      }),
    [],
  );

  if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <div ref={scrollContainerRef} className="flex-1 overflow-auto px-5 pb-5">
      <Table
        data={projectedOrgs}
        fields={orgSchema.fields}
        viewProps={viewProps}
        scrollContainerRef={scrollContainerRef}
        rowEvents={rowEvents}
        isSortable={false}
        extraRightControls={
          <>
            <div className="relative flex flex-1">
              <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
              <PlainInput
                placeholder="Find org"
                onChange={(e) => setSearchDebounced(e.target.value)}
                className="h-7 flex-1 border-0 pl-7 outline-none transition-all bg-transparent hover:bg-primary-100 focus:bg-primary-100"
              />
            </div>
          </>
        }
        formatters={formatters}
        tableType="list"
      />
    </div>
  );
}

function formatSpanLimit(value: number): string {
  if (value >= 1_000_000) {
    return `${(value / 1_000_000).toLocaleString()}M`;
  }
  return `${(value / 1000).toLocaleString()}k`;
}

export function SpanLimitFormatter<
  TsTable extends OrgRow & {
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>({ value }: FormatterProps<TsTable, TsValue>) {
  return value === null ? (
    <span className="text-primary-500">Unlimited</span>
  ) : (
    <span className="text-primary-500">
      {value === 1000 || value === 250_000 || value === 1_000_000 ? "Free" : ""}{" "}
      ({formatSpanLimit(value)} spans/month)
    </span>
  );
}

function formatLogBytesLimit(value: number): string {
  // Special case free tier to format as 1GB instead of 1073741824B
  if (value === FREE_TIER_LOG_BYTES_LIMIT) {
    return "1GB";
  }
  if (value >= ONE_GIB) {
    return `${(value / ONE_GIB).toLocaleString()}GB`;
  }
  if (value >= ONE_MIB) {
    return `${(value / ONE_MIB).toLocaleString()}MB`;
  }
  if (value >= ONE_KIB) {
    return `${(value / ONE_KIB).toLocaleString()}KB`;
  }
  return `${value.toLocaleString()}B`;
}

export function LogBytesLimitFormatter<
  TsTable extends OrgRow & {
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>({ value }: FormatterProps<TsTable, TsValue>) {
  return value === null ? (
    <span className="text-primary-500">Unlimited</span>
  ) : (
    <span className="text-primary-500">
      {value === FREE_TIER_LOG_BYTES_LIMIT ? "Free" : ""} (
      {formatLogBytesLimit(value)}/month)
    </span>
  );
}

export function TelemetryUrlFormatter<
  TsTable extends OrgRow & {
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>({ value }: FormatterProps<TsTable, TsValue>) {
  return value ? (
    <span className="text-primary-500">{value}</span>
  ) : (
    <span className="text-primary-400">Not configured</span>
  );
}

export function PlanIdFormatter<
  TsTable extends OrgRow & {
    [BT_IS_GROUP]?: boolean;
  },
  TsValue,
>({ value }: FormatterProps<TsTable, TsValue>) {
  return value ? (
    <span className="text-primary-500">{getLabelForPlanId(value)}</span>
  ) : (
    <span className="text-primary-400">Not configured</span>
  );
}
