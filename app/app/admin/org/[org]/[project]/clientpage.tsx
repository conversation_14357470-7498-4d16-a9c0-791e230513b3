"use client";

import { BrainstoreProjectConfiguration } from "#/app/app/[org]/p/[project]/configuration/brainstore/brainstore-project-configuration";
import {
  type adminFetchAllProjects,
  type ProjectRow,
  type adminFetchObjects,
  type ObjectRow,
  type adminFetchAllOrgs,
  type OrgRow,
  type adminRestoreObject,
} from "../../../actions";
import { useQueryFunc } from "#/utils/react-query";
import { useMemo, useRef, useState } from "react";
import { type FormatterProps, Table } from "#/ui/arrow-table";
import { useIsClient } from "#/utils/use-is-client";
import { useViewStates } from "#/utils/view/use-view";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import Fuse from "fuse.js";
import { Schema } from "apache-arrow";
import { Field } from "apache-arrow";
import { Utf8 } from "apache-arrow";
import { Search, ExternalLinkIcon, Clipboard } from "lucide-react";
import { PlainInput } from "#/ui/plain-input";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { BrainstoreObjectConfiguration } from "#/app/app/[org]/p/[project]/brainstore/[object]/brainstore-object-configuration";
import Link from "next/link";
import { parseAsString, useQueryState } from "nuqs";
import { ExternalLink } from "#/ui/link";
import { Button } from "#/ui/button";
import { toast } from "sonner";
import type { FormatterMeta } from "#/ui/field-to-column";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";

const objectSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "object_type", type: new Utf8() }),
  Field.new({ name: "name", type: new Utf8() }),
  Field.new({ name: "object_id", type: new Utf8() }),
  Field.new({ name: "created", type: new Utf8() }),
  Field.new({ name: "backfill_status", type: new Utf8() }),
  Field.new({ name: "app_link", type: new Utf8() }),
  Field.new({ name: "deleted_at", type: new Utf8() }),
]);

export function ClientPage({
  orgName,
  projectName,
  orgRows: orgRowsProp,
  projectRows: projectRowsProp,
  objectRows: objectRowsProp,
}: {
  orgName: string;
  projectName: string;
  orgRows: OrgRow[];
  projectRows: ProjectRow[];
  objectRows: ObjectRow[];
}) {
  const { data: orgInfoRows } = useQueryFunc<typeof adminFetchAllOrgs>({
    fName: "adminFetchAllOrgs",
    args: {
      name: orgRowsProp[0].name,
    },
    serverData: orgRowsProp,
  });
  const orgInfo = useMemo(() => orgInfoRows?.[0], [orgInfoRows]);

  const { data: projectRows } = useQueryFunc<typeof adminFetchAllProjects>({
    fName: "adminFetchAllProjects",
    args: {
      orgName,
      projectName,
    },
    serverData: projectRowsProp,
  });
  const project = useMemo(() => projectRows?.[0], [projectRows]);

  const { data: objectRowsRaw, refetch: refetchObjectRows } = useQueryFunc<
    typeof adminFetchObjects
  >({
    fName: "adminFetchObjects",
    args: {
      orgName,
      projectName,
    },
    serverData: objectRowsProp,
  });

  const isClient = useIsClient();

  const [search, setSearch] = useQueryState("search", parseAsString);
  const viewProps = useViewStates({
    pageIdentifier: "admin-project",
    viewParams: undefined,
    clauseChecker: null,
  });

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const objectRows = useMemo(() => {
    return objectRowsRaw?.map((object) => ({
      ...object,
      backfill_status: "",
      app_link: `/app/${encodeURIComponent(orgName)}/object?object_type=${encodeURIComponent(object.object_type)}&object_id=${encodeURIComponent(object.id)}`,
    }));
  }, [objectRowsRaw, orgName]);

  const fuse = useMemo(
    () =>
      new Fuse(objectRows, {
        keys: ["name", "object_type", "object_id"],
        threshold: 0.0,
        ignoreLocation: true,
        includeMatches: true,
      }),
    [objectRows],
  );
  const filteredObjects = useMemo(() => {
    if (!search) return objectRows;
    const results = fuse.search(search);
    return results.map((result) => result.item);
  }, [search, fuse, objectRows]);

  const [openedObjectId, setOpenedObjectId] = useState<string | null>(null);
  const [undeleteObjectId, setUndeleteObjectId] = useState<{
    objectType: ObjectRow["object_type"];
    objectId: ObjectRow["id"];
  } | null>(null);

  const formatters = useMemo(() => {
    return makeFormatterMap<ObjectRow & { app_link: string }, string>({
      id: makeGrayIfDeletedFormatter("id"),
      name: makeGrayIfDeletedFormatter("name"),
      object_type: makeGrayIfDeletedFormatter("object_type"),
      object_id: makeGrayIfDeletedFormatter("object_id"),
      created: makeGrayIfDeletedFormatter("created"),
      deleted_at: {
        cell: (props) => (
          <DeletedAtFormatter
            {...props}
            setUndeleteObjectId={setUndeleteObjectId}
          />
        ),
      },
      backfill_status: {
        cell: (props) => (
          <ObjectBackfillStatusFormatter
            {...props}
            setOpenedObjectId={setOpenedObjectId}
          />
        ),
      },
      app_link: {
        cell: ObjectLinkFormatter,
      },
    });
  }, []);

  const { getToken } = useAuth();

  if (!project) {
    return <div>Project not found</div>;
  } else if (!isClient) {
    return <TableSkeleton />;
  }

  return (
    <div ref={scrollContainerRef} className="relative flex-1 overflow-auto">
      <div className="flex flex-row gap-8 p-5">
        <div className="flex flex-col">
          <div className="mb-2 text-sm text-primary-500">Org id</div>
          <div className="mb-2 text-sm">
            <Button
              onClick={(e) => {
                e.preventDefault();
                navigator.clipboard.writeText(orgInfo.id ?? "");
                toast("Organization ID copied to clipboard");
              }}
              size="inline"
              className="border-0 text-xs font-normal text-primary-500"
              IconRight={Clipboard}
            >
              {orgInfo.id}
            </Button>
          </div>
        </div>
        <div className="flex flex-col">
          <div className="mb-2 text-sm text-primary-500">Project id</div>
          <div className="mb-2 text-sm">
            <Button
              onClick={(e) => {
                e.preventDefault();
                navigator.clipboard.writeText(project.id ?? "");
                toast("Project ID copied to clipboard");
              }}
              size="inline"
              className="border-0 text-xs font-normal text-primary-500"
              IconRight={Clipboard}
            >
              {project.id}
            </Button>
          </div>
        </div>
      </div>

      <div className="sticky left-0 px-5">
        <div className="rounded-md p-5 bg-primary-100">
          <BrainstoreProjectConfiguration
            orgId={project.org_id}
            projectId={project.id}
            apiUrl={orgInfo.api_url ?? undefined}
          />
        </div>
      </div>
      <div className="p-5">
        <Table
          data={filteredObjects}
          fields={objectSchema.fields}
          viewProps={viewProps}
          scrollContainerRef={scrollContainerRef}
          extraRightControls={
            <>
              <div className="relative flex flex-1">
                <Search className="pointer-events-none absolute left-2 top-[8px] size-3 text-primary-500" />
                <PlainInput
                  placeholder="Find object"
                  value={search ?? ""}
                  onChange={(e) => setSearch(e.target.value)}
                  className="h-7 flex-1 border-0 pl-7 outline-none transition-all bg-transparent hover:bg-primary-100 focus:bg-primary-100"
                />
              </div>
            </>
          }
          formatters={formatters}
          tableType="list"
        />
      </div>
      {openedObjectId && (
        <BrainstoreObjectConfiguration
          objectId={openedObjectId}
          onOpenChange={() => setOpenedObjectId(null)}
          apiUrl={orgInfo.api_url ?? undefined}
        />
      )}
      {undeleteObjectId && (
        <ConfirmationDialog
          title="Restore object"
          description={`Are you sure you want to restore ${undeleteObjectId.objectType} ${undeleteObjectId.objectId}?`}
          onConfirm={async () => {
            setUndeleteObjectId(null);
            await invokeServerAction<typeof adminRestoreObject>({
              fName: "adminRestoreObject",
              args: {
                objectType: undeleteObjectId.objectType,
                objectId: undeleteObjectId.objectId,
              },
              getToken,
            });
            refetchObjectRows();
          }}
          onCancel={() => setUndeleteObjectId(null)}
          open={!!undeleteObjectId}
          onOpenChange={() => setUndeleteObjectId(null)}
          confirmText="Restore"
        />
      )}
    </div>
  );
}

function ObjectBackfillStatusFormatter<TsTable extends ObjectRow, TsValue>({
  cell,
  setOpenedObjectId,
}: FormatterProps<TsTable, TsValue> & {
  setOpenedObjectId: (objectId: string) => void;
}) {
  return (
    <Link
      onClick={(e) => {
        e.preventDefault();
        setOpenedObjectId(cell.row.original.object_id);
      }}
      href="#"
      className="text-blue-500"
    >
      Status
    </Link>
  );
}

function ObjectLinkFormatter<
  TsTable extends ObjectRow & { app_link: string },
  TsValue,
>({ cell }: FormatterProps<TsTable, TsValue>) {
  return (
    <ExternalLink
      href={cell.row.original.app_link}
      className="inline-flex items-center gap-1 text-blue-500"
      target="_blank"
    >
      Open <ExternalLinkIcon className="size-3" />
    </ExternalLink>
  );
}

function makeGrayIfDeletedFormatter<
  TsTable extends ObjectRow,
  TsValue,
  TField extends keyof TsTable,
>(field: TField): FormatterMeta<TsTable, TsValue> {
  return {
    cell: (props: FormatterProps<TsTable, TsValue>) => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const value = props.row.original[field] as string;
      if (props.row.original.deleted_at) {
        return <div className="text-gray-500">{value}</div>;
      }
      return value;
    },
  };
}

function DeletedAtFormatter<
  TsTable extends ObjectRow & { app_link: string },
  TsValue,
>({
  cell,
  setUndeleteObjectId,
}: FormatterProps<TsTable, TsValue> & {
  setUndeleteObjectId: (object: {
    objectType: ObjectRow["object_type"];
    objectId: ObjectRow["id"];
  }) => void;
}) {
  return (
    <>
      {cell.row.original.deleted_at && (
        <div className="text-gray-500">
          <Link
            href="#"
            className="text-blue-500"
            onClick={(e) => {
              e.preventDefault();
              setUndeleteObjectId({
                objectType: cell.row.original.object_type,
                objectId: cell.row.original.id,
              });
            }}
          >
            Restore
          </Link>{" "}
          (deleted at {cell.row.original.deleted_at})
        </div>
      )}
    </>
  );
}
