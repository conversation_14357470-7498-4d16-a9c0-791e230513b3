import { isEmpty } from "@braintrust/core";
import { z } from "zod";
import { type PreviewSpan, type Span } from "./graph";

const flattenedRootDataSchema = z.object({
  metrics: z.object({
    start: z.number().optional(),
    end: z.number().optional(),
  }),
});

export const getTraceDurations = ({
  flattenedRootData,
  calculateTotalDuration,
  rootChildren,
}: {
  flattenedRootData: unknown;
  calculateTotalDuration?: boolean;
  rootChildren?: Span[] | PreviewSpan[];
}) => {
  const { success, data } =
    flattenedRootDataSchema.safeParse(flattenedRootData);
  const traceStart = success ? data.metrics.start : undefined;
  const traceEnd = success ? data.metrics.end : undefined;

  const findMaxEndTimeRecursive = (
    spans: Span[] | PreviewSpan[],
  ): number | undefined => {
    let maxEnd: number | undefined = undefined;

    for (const span of spans) {
      const spanEnd = span.data.metrics?.end;
      if (!isEmpty(spanEnd) && (isEmpty(maxEnd) || spanEnd > maxEnd)) {
        maxEnd = spanEnd;
      }
    }

    return maxEnd;
  };

  const maxEndFromChildren =
    calculateTotalDuration && rootChildren && rootChildren.length > 0
      ? findMaxEndTimeRecursive(rootChildren)
      : undefined;

  const effectiveTraceEnd = Math.max(maxEndFromChildren ?? 0, traceEnd ?? 0);

  const totalDuration =
    calculateTotalDuration &&
    !isEmpty(effectiveTraceEnd) &&
    !isEmpty(traceStart)
      ? effectiveTraceEnd - traceStart
      : undefined;

  return { traceStart, traceEnd, totalDuration };
};
