import { fetchInferBtql, type InferSchema } from "#/utils/btql/btql";
import { useMemo } from "react";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { useQuery } from "@tanstack/react-query";
import { withErrorTiming } from "#/utils/btapi/type-error";

export function useInferDatasetPaths({ datasetId }: { datasetId?: string }) {
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const btqlFlags = useBtqlFlags();
  const hasSchemaInference = useIsFeatureEnabled("schemaInference");

  const { data: inferSchemaData } = useQuery({
    queryKey: ["inferDatasetPathsSchema", datasetId],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        datasetId
          ? await fetchInferBtql({
              args: {
                query: {
                  from: Query.from("dataset", [datasetId]),
                  filter: {
                    op: "eq",
                    left: { op: "ident", name: ["is_root"] },
                    right: { op: "literal", value: true },
                  },
                  infer: [
                    { op: "ident" as const, name: ["input"] },
                    { op: "ident" as const, name: ["expected"] },
                    { op: "ident" as const, name: ["metadata"] },
                  ],
                  sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                },
                brainstoreRealtime: true,
              },
              btqlFlags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              signal,
            })
          : null,
      "Infer dataset paths (schema)",
      {
        objectType: "dataset",
      },
    ),
    enabled: !!datasetId && hasSchemaInference,
    gcTime: 1000 * 30,
    staleTime: 1000 * 60,
  });

  return useMemo(() => {
    if (!inferSchemaData) {
      return;
    }

    return processInferredDatasetPaths(inferSchemaData.data).paths;
  }, [inferSchemaData]);
}

function processInferredDatasetPaths(inferSchemaData: InferSchema[]) {
  const paths: string[] = [];
  const stringOptions: Record<string, string[]> = {};

  for (const schema of inferSchemaData) {
    const pathParts = schema.name;
    const pathKey = pathParts.join(".");

    // Add all path variations (parent paths + full path)
    for (let i = 1; i <= pathParts.length; i++) {
      const partialPath = pathParts.slice(0, i).join(".");
      if (!paths.includes(partialPath)) {
        paths.push(partialPath);
      }
    }

    const isStringField = schema.type?.type === "string";
    const isInputOrExpected =
      pathParts.length >= 1 &&
      (pathParts[0] === "input" || pathParts[0] === "expected");

    if (isStringField && isInputOrExpected && schema.top_values?.length > 0) {
      const stringValues = schema.top_values
        .map((tv) => tv.value)
        .filter((value): value is string => typeof value === "string")
        .slice(0, 20); // Limit to first 20 options to avoid overwhelming UI

      if (stringValues.length > 0) {
        stringOptions[pathKey] = stringValues;
      }
    }
  }

  paths.sort();

  return {
    stringOptions,
    paths,
  };
}
