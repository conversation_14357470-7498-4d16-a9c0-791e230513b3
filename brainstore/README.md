# Brainstore

## CLI

You should install the CLI with `cargo install --path cli`, and make sure the .cargo/bin directory is in your PATH.

```bash
brainstore --help
```

To confirm it's working.

## Examples / expect tests

See the [examples](./examples) directory for examples of BTQL queries. The [`expect_tests.rs`](./query/src/expect_tests.rs) file will run the examples and verify the output against the `*.btql.expected` files.

### Adding tests

Add to existing `*.btql` files with each line being its own query. You can review the `events.jsonl` file for logs that will be used as part of the test (to query against).

### Running the expect tests

You should use the `brainstore test` command to run the expect tests. Go to the directory of the tests you want to run and run:

```bash
cd examples # or examples/scores, etc.
brainstore btql test --default-opts .
```

### Updating the expect tests

Run the test with the optional `--update` flag to update the expect tests.

```bash
brainstore btql test --default-opts . --update
```
