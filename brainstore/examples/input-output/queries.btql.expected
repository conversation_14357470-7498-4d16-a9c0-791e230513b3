[{"error": null, "query": "from: dataset('singleton') | select: id | filter: input='foo'", "result_rows": [{"id": "1"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: id | filter: input.field='foo'", "result_rows": [{"id": "2"}], "skip": false}, {"error": null, "query": "-- These should run the ternary expressions\nfrom: dataset('singleton') spans | select: id, is_root ? input : null as r", "result_rows": [{"id": "1", "r": "foo"}, {"id": "2"}, {"id": "3"}, {"id": "4", "r": "new request"}, {"id": "5"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: id, is_root ? null : input as r", "result_rows": [{"id": "1"}, {"id": "2", "r": {"field": "foo"}}, {"id": "3", "r": "process nested"}, {"id": "4"}, {"id": "5", "r": "sub task"}, {"id": "6"}, {"id": "7"}], "skip": false}, {"error": null, "query": "-- These should optimize away the ternary expression from the plan\nfrom: dataset('singleton') traces | select: id, is_root ? input : null as r | filter: id=1", "result_rows": [{"id": "1", "r": "foo"}, {"id": "2"}, {"id": "3"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') traces | select: id, is_root ? null : input as r | filter: id=1", "result_rows": [{"id": "1"}, {"id": "2", "r": {"field": "foo"}}, {"id": "3", "r": "process nested"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: sha256(null)", "result_rows": [{"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}, {"sha256(null)": "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: id, sha256(id)", "result_rows": [{"id": "1", "sha256(id)": "391552c099c101b131feaf24c5795a6a15bc8ec82015424e0d2b4274a369a0bf"}, {"id": "2", "sha256(id)": "cc11310c456c3690d5a74c23aa31cc25b4e74cdae146c71e54ad9dbc1d109fde"}, {"id": "3", "sha256(id)": "a4aab3f1f08004e907d2357fafe74cab56359bcb32e23f52e7eb1d3a9c0a2ad3"}, {"id": "4", "sha256(id)": "2bf175f9655e7bb7357b9f0a7c6051465a5ae701104ffe741b98e852c0e4d460"}, {"id": "5", "sha256(id)": "d10a4bc9e0c1fa4e8f3d7ce2512b8756e47ca5fa451f373c39a1431bb88db49f"}, {"id": "6", "sha256(id)": "92e9e7e5922d26e17e48f0869ab25cc99499fdab722c065de8e0965c96c68e86"}, {"id": "7", "sha256(id)": "266aa5886067fbbc1a3a39fe432fc5bbf561abc90e4b8a322215ac17df6ce012"}], "skip": false}]