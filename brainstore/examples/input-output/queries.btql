from: dataset('singleton') | select: id | filter: input='foo';
from: dataset('singleton') | select: id | filter: input.field='foo';

-- These should run the ternary expressions
from: dataset('singleton') spans | select: id, is_root ? input : null as r;
from: dataset('singleton') spans | select: id, is_root ? null : input as r;

-- These should optimize away the ternary expression from the plan
from: dataset('singleton') traces | select: id, is_root ? input : null as r | filter: id=1;
from: dataset('singleton') traces | select: id, is_root ? null : input as r | filter: id=1;

from: dataset('singleton') spans | select: sha256(null);
from: dataset('singleton') spans | select: id, sha256(id);
