#[cfg(test)]
mod like {
    use crate::interpreter::expr::perform_like;
    use util::Value;

    // Test case structure
    struct LikeTestCase {
        left: &'static str,
        right: &'static str,
        case_insensitive: bool,
        expected: Option<bool>,
        description: &'static str,
    }

    fn str_to_value(s: &str) -> Value {
        Value::String(s.to_string())
    }

    // All test cases in one place
    fn get_test_cases() -> Vec<LikeTestCase> {
        vec![
            // Exact matches
            LikeTestCase {
                left: "hello",
                right: "hello",
                case_insensitive: false,
                expected: Some(true),
                description: "exact match, case sensitive",
            },
            LikeTestCase {
                left: "hello",
                right: "world",
                case_insensitive: false,
                expected: Some(false),
                description: "exact non-match, case sensitive",
            },
            // Case sensitivity tests
            LikeTestCase {
                left: "Hello",
                right: "hello",
                case_insensitive: false,
                expected: Some(false),
                description: "different case, case sensitive",
            },
            LikeTestCase {
                left: "Hello",
                right: "hello",
                case_insensitive: true,
                expected: Some(true),
                description: "different case, case insensitive",
            },
            LikeTestCase {
                left: "WORLD",
                right: "world",
                case_insensitive: true,
                expected: Some(true),
                description: "all caps, case insensitive",
            },
            // Wildcard at start
            LikeTestCase {
                left: "hello world",
                right: "%world",
                case_insensitive: false,
                expected: Some(true),
                description: "wildcard at start, matching",
            },
            LikeTestCase {
                left: "hello earth",
                right: "%world",
                case_insensitive: false,
                expected: Some(false),
                description: "wildcard at start, non-matching",
            },
            LikeTestCase {
                left: "hello WORLD",
                right: "%world",
                case_insensitive: true,
                expected: Some(true),
                description: "wildcard at start, case insensitive matching",
            },
            LikeTestCase {
                left: "hello EARTH",
                right: "%world",
                case_insensitive: true,
                expected: Some(false),
                description: "wildcard at start, case insensitive non-matching",
            },
            // Wildcard at end
            LikeTestCase {
                left: "hello world",
                right: "hello%",
                case_insensitive: false,
                expected: Some(true),
                description: "wildcard at end, matching",
            },
            LikeTestCase {
                left: "goodbye world",
                right: "hello%",
                case_insensitive: false,
                expected: Some(false),
                description: "wildcard at end, non-matching",
            },
            LikeTestCase {
                left: "HELLO world",
                right: "hello%",
                case_insensitive: true,
                expected: Some(true),
                description: "wildcard at end, case insensitive matching",
            },
            LikeTestCase {
                left: "GOODBYE world",
                right: "hello%",
                case_insensitive: true,
                expected: Some(false),
                description: "wildcard at end, case insensitive non-matching",
            },
            // Wildcard in middle
            LikeTestCase {
                left: "hello dear world",
                right: "hello%world",
                case_insensitive: false,
                expected: Some(true),
                description: "wildcard in middle with content",
            },
            LikeTestCase {
                left: "beautiful hello world",
                right: "hello%world",
                case_insensitive: false,
                expected: Some(false),
                description: "wildcard in middle with different content",
            },
            LikeTestCase {
                left: "HELLO dear WORLD",
                right: "hello%world",
                case_insensitive: true,
                expected: Some(true),
                description: "wildcard in middle, case insensitive matching",
            },
            LikeTestCase {
                left: "BEAUTIFUL hello world",
                right: "hello%world",
                case_insensitive: true,
                expected: Some(false),
                description: "wildcard in middle, case insensitive non-matching",
            },
            // Multiple wildcards
            LikeTestCase {
                left: "hello dear sweet world",
                right: "%dear%world",
                case_insensitive: false,
                expected: Some(true),
                description: "multiple wildcards, matching",
            },
            LikeTestCase {
                left: "hello bear sweet earth",
                right: "%dear%world",
                case_insensitive: false,
                expected: Some(false),
                description: "multiple wildcards, non-matching",
            },
            LikeTestCase {
                left: "hello DEAR sweet WORLD",
                right: "%dear%world",
                case_insensitive: true,
                expected: Some(true),
                description: "multiple wildcards, case insensitive matching",
            },
            LikeTestCase {
                left: "hello BEAR sweet EARTH",
                right: "%dear%world",
                case_insensitive: true,
                expected: Some(false),
                description: "multiple wildcards, case insensitive non-matching",
            },
            // No wildcards with middle content
            LikeTestCase {
                left: "hello beautiful world",
                right: "hello world",
                case_insensitive: false,
                expected: Some(false),
                description: "no wildcards with extra middle content",
            },
            LikeTestCase {
                left: "HELLO WORLD",
                right: "hello world",
                case_insensitive: true,
                expected: Some(true),
                description: "no wildcards, case insensitive matching",
            },
            LikeTestCase {
                left: "HELLO BEAUTIFUL WORLD",
                right: "hello world",
                case_insensitive: true,
                expected: Some(false),
                description: "no wildcards with extra middle content, case insensitive",
            },
            // Empty strings
            LikeTestCase {
                left: "",
                right: "",
                case_insensitive: false,
                expected: Some(true),
                description: "empty strings match",
            },
            LikeTestCase {
                left: "",
                right: "%",
                case_insensitive: false,
                expected: Some(true),
                description: "empty string matches wildcard",
            },
            LikeTestCase {
                left: "hello",
                right: "",
                case_insensitive: false,
                expected: Some(false),
                description: "non-empty string doesn't match empty pattern",
            },
        ]
    }

    #[test]
    fn test_like() {
        for test_case in get_test_cases() {
            let result = perform_like(
                &str_to_value(test_case.left),
                &str_to_value(test_case.right),
                test_case.case_insensitive,
            );
            assert_eq!(
                result,
                test_case.expected,
                "Failed test: {} \nInput: '{}' {} '{}'\nExpected: {:?}, Got: {:?}",
                test_case.description,
                test_case.left,
                if test_case.case_insensitive {
                    "ILIKE"
                } else {
                    "LIKE"
                },
                test_case.right,
                test_case.expected,
                result
            );
        }
    }
}

#[cfg(test)]
mod datetime {
    use crate::{binder::ast::ArithmeticOp, interpreter::expr::perform_arithmetic_op};

    struct DateTimeTestCase {
        left: &'static str,
        right: &'static str,
        op: ArithmeticOp,
        expected: Option<&'static str>,
        expect_error: bool,
        description: &'static str,
    }

    fn get_datetime_test_cases() -> Vec<DateTimeTestCase> {
        vec![
            // Month length clamping tests
            DateTimeTestCase {
                left: "2024-01-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Add,
                expected: Some("2024-02-29T12:00:00Z"), // Jan 31 -> Feb 29 (leap year)
                expect_error: false,
                description: "31st to February (leap year)",
            },
            DateTimeTestCase {
                left: "2023-01-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Add,
                expected: Some("2023-02-28T12:00:00Z"), // Jan 31 -> Feb 28 (non-leap year)
                expect_error: false,
                description: "31st to February (non-leap year)",
            },
            DateTimeTestCase {
                left: "2024-03-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Add,
                expected: Some("2024-04-30T12:00:00Z"), // Mar 31 -> Apr 30
                expect_error: false,
                description: "31st to 30-day month",
            },
            DateTimeTestCase {
                left: "2024-08-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Add,
                expected: Some("2024-09-30T12:00:00Z"), // Aug 31 -> Sep 30
                expect_error: false,
                description: "31st to another 30-day month",
            },
            // Leap year Feb 29 handling
            DateTimeTestCase {
                left: "2024-02-29T12:00:00Z",
                right: "1 year",
                op: ArithmeticOp::Add,
                expected: Some("2025-02-28T12:00:00Z"), // Year-based interval
                expect_error: false,
                description: "leap year Feb 29 + 1 year",
            },
            DateTimeTestCase {
                left: "2024-02-29T12:00:00Z",
                right: "12 months",
                op: ArithmeticOp::Add,
                expected: Some("2025-02-28T12:00:00Z"), // Month-based interval
                expect_error: false,
                description: "leap year Feb 29 + 12 months",
            },
            DateTimeTestCase {
                left: "2024-02-29T12:00:00Z",
                right: "48 months",
                op: ArithmeticOp::Add,
                expected: Some("2028-02-29T12:00:00Z"), // Should keep Feb 29 in next leap year
                expect_error: false,
                description: "leap year Feb 29 + 48 months (to next leap year)",
            },
            // Addition of time units
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 microsecond",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-14T12:00:00.000001Z"),
                expect_error: false,
                description: "add microsecond",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 millisecond",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-14T12:00:00.001Z"),
                expect_error: false,
                description: "add millisecond",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 second",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-14T12:00:01Z"),
                expect_error: false,
                description: "add second",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 minute",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-14T12:01:00Z"),
                expect_error: false,
                description: "add minute",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 hour",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-14T13:00:00Z"),
                expect_error: false,
                description: "add hour",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-15T12:00:00Z"),
                expect_error: false,
                description: "add day",
            },
            // Timezone offset tests
            DateTimeTestCase {
                left: "2024-03-14T12:00:00+05:00",
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-15T12:00:00+05:00"),
                expect_error: false,
                description: "add day with positive timezone",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00-08:00",
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-15T12:00:00-08:00"),
                expect_error: false,
                description: "add day with negative timezone",
            },
            DateTimeTestCase {
                left: "2024-03-14T23:00:00+05:00",
                right: "2 hours",
                op: ArithmeticOp::Add,
                expected: Some("2024-03-15T01:00:00+05:00"),
                expect_error: false,
                description: "add hours crossing day boundary with timezone",
            },
            // Subtracting into a shorter month
            DateTimeTestCase {
                left: "2024-12-31T00:00:00Z",
                right: "8 months",
                op: ArithmeticOp::Sub,
                expected: Some("2024-04-30T00:00:00Z"), // Dec 31 -> Apr 30
                expect_error: false,
                description: "subtract months into a shorter month",
            },
            DateTimeTestCase {
                left: "2024-03-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Sub,
                expected: Some("2024-02-29T12:00:00Z"), // Mar 31 -> Feb 29 (leap year)
                expect_error: false,
                description: "subtract month from Mar 31st to Feb (leap year)",
            },
            DateTimeTestCase {
                left: "2023-03-31T12:00:00Z",
                right: "1 month",
                op: ArithmeticOp::Sub,
                expected: Some("2023-02-28T12:00:00Z"), // Mar 31 -> Feb 28 (non-leap year)
                expect_error: false,
                description: "subtract month from Mar 31st to Feb (non-leap year)",
            },
            DateTimeTestCase {
                left: "2024-02-29T00:00:00Z",
                right: "1 year",
                op: ArithmeticOp::Sub,
                expected: Some("2023-02-28T00:00:00Z"), // Feb 29 -> Feb 28 (non-leap year)
                expect_error: false,
                description: "subtract year from Feb 29th to Feb 28th (non-leap year)",
            },
            // Crossing day/month/year boundaries (addition)
            DateTimeTestCase {
                left: "2024-01-31T23:59:59.999999Z",
                right: "1 microsecond",
                op: ArithmeticOp::Add,
                expected: Some("2024-02-01T00:00:00Z"),
                expect_error: false,
                description: "add microsecond crossing day boundary",
            },
            DateTimeTestCase {
                left: "2024-01-31T23:59:59.999Z",
                right: "1 millisecond",
                op: ArithmeticOp::Add,
                expected: Some("2024-02-01T00:00:00Z"),
                expect_error: false,
                description: "add millisecond crossing month boundary",
            },
            DateTimeTestCase {
                left: "2024-12-31T23:59:59Z",
                right: "1 second",
                op: ArithmeticOp::Add,
                expected: Some("2025-01-01T00:00:00Z"),
                expect_error: false,
                description: "add second crossing year boundary",
            },
            // Crossing day/month/year boundaries (subtraction)
            DateTimeTestCase {
                left: "2024-02-01T00:00:00Z",
                right: "1 microsecond",
                op: ArithmeticOp::Sub,
                expected: Some("2024-01-31T23:59:59.999999Z"),
                expect_error: false,
                description: "subtract microsecond crossing day boundary",
            },
            DateTimeTestCase {
                left: "2024-02-01T00:00:00Z",
                right: "1 millisecond",
                op: ArithmeticOp::Sub,
                expected: Some("2024-01-31T23:59:59.999Z"),
                expect_error: false,
                description: "subtract millisecond crossing month boundary",
            },
            DateTimeTestCase {
                left: "2025-01-01T00:00:00Z",
                right: "1 second",
                op: ArithmeticOp::Sub,
                expected: Some("2024-12-31T23:59:59Z"),
                expect_error: false,
                description: "subtract second crossing year boundary",
            },
            // Invalid strings are treated as null
            DateTimeTestCase {
                left: "invalid datetime",
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "invalid datetime string",
            },
            DateTimeTestCase {
                left: "2024-13-14T12:00:00Z", // invalid month
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid month (13)",
            },
            DateTimeTestCase {
                left: "2024-11-31T12:00:00Z", // Nov only has 30 days
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid day (Nov 31)",
            },
            DateTimeTestCase {
                left: "2023-02-29T12:00:00Z", // not a leap year
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid leap day (2023)",
            },
            DateTimeTestCase {
                left: "2024-04-31T12:00:00Z", // April has 30 days
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid day (Apr 31)",
            },
            DateTimeTestCase {
                left: "2024-03-14T25:00:00Z", // invalid hour
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid hour (25)",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:60:00Z", // invalid minute
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime with invalid minute (60)",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00", // missing timezone
                right: "1 day",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "datetime missing timezone specifier",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "invalid interval",
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "completely invalid interval string",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1.5 day", // fractional not supported
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "interval with unsupported fractional value",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 nanosecond", // unsupported unit
                op: ArithmeticOp::Add,
                expected: None,
                expect_error: false,
                description: "interval with unsupported unit",
            },
            // Error cases
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 second",
                op: ArithmeticOp::Mul,
                expected: None,
                expect_error: true,
                description: "invalid arithmetic op (mul)",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 minute",
                op: ArithmeticOp::Div,
                expected: None,
                expect_error: true,
                description: "invalid arithmetic op (div)",
            },
            DateTimeTestCase {
                left: "2024-03-14T12:00:00Z",
                right: "1 year",
                op: ArithmeticOp::Mod,
                expected: None,
                expect_error: true,
                description: "invalid arithmetic op (mod)",
            },
        ]
    }

    #[test]
    fn test_datetime_arithmetic() {
        for test_case in get_datetime_test_cases() {
            let result = perform_arithmetic_op(
                &util::Value::String(test_case.left.to_string()),
                &util::Value::String(test_case.right.to_string()),
                &test_case.op,
            );

            if test_case.expect_error {
                assert!(
                    result.is_err(),
                    "Expected error but got {:?} for test case: {}",
                    result,
                    test_case.description
                );
                continue;
            }

            match (result, test_case.expected) {
                (Ok(util::Value::String(result_str)), Some(expected)) => {
                    assert_eq!(
                        result_str,
                        expected,
                        "Failed test: {} \nInput: '{}' {:?} '{}'\nExpected: {}, Got: {}",
                        test_case.description,
                        test_case.left,
                        test_case.op,
                        test_case.right,
                        expected,
                        result_str
                    );
                }
                (Ok(util::Value::Null), None) => {
                    // Expected null result
                    continue;
                }
                (Ok(util::Value::Null), Some(expected)) => {
                    panic!(
                        "Expected success with {} but got null for test case: {}",
                        expected, test_case.description
                    );
                }
                (Err(e), _) => {
                    panic!(
                        "Unexpected error ({}) for test case: {}",
                        e, test_case.description
                    );
                }
                (Ok(unexpected), _) => {
                    panic!(
                        "Got unexpected value type {:?} for test case: {}",
                        unexpected, test_case.description
                    );
                }
            }
        }
    }
}

#[cfg(test)]
mod truncate_timestamp {
    use crate::interpreter::expr::truncate_timestamp_with_tz_offset;
    use time::OffsetDateTime;

    struct TruncateTestCase {
        timestamp: &'static str,
        truncate_to: &'static str,
        tz_offset: Option<i16>,
        expected: Option<&'static str>,
        description: &'static str,
    }

    const TRUNCATE_TEST_CASES: &[TruncateTestCase] = &[
        // Year truncation
        TruncateTestCase {
            timestamp: "2024-12-31T23:58:00Z",
            truncate_to: "year",
            tz_offset: Some(-1),
            expected: Some("2023-12-31T23:59:00Z"),
            description: "year truncation with +1 minute staying in current year",
        },
        TruncateTestCase {
            timestamp: "2024-12-31T23:58:00Z",
            truncate_to: "year",
            tz_offset: Some(-3),
            expected: Some("2024-12-31T23:57:00Z"),
            description: "year truncation with +3 minutes crossing to next year",
        },
        TruncateTestCase {
            timestamp: "2024-01-01T00:02:00Z",
            truncate_to: "year",
            tz_offset: Some(2),
            expected: Some("2024-01-01T00:02:00Z"),
            description: "year truncation with -2 minutes staying in current year",
        },
        TruncateTestCase {
            timestamp: "2024-01-01T00:02:00Z",
            truncate_to: "year",
            tz_offset: Some(3),
            expected: Some("2023-01-01T00:03:00Z"),
            description: "year truncation with -3 minutes crossing to previous year",
        },
        // Month boundary cases
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:00Z",
            truncate_to: "month",
            tz_offset: Some(-1),
            expected: Some("2024-01-31T23:59:00Z"),
            description: "month truncation with +1 minute staying in current month",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:00Z",
            truncate_to: "month",
            tz_offset: Some(-3),
            expected: Some("2024-02-29T23:57:00Z"),
            description: "month truncation with +3 minutes crossing to previous month",
        },
        // Week truncation
        TruncateTestCase {
            timestamp: "2024-02-19T00:02:00Z", // Monday
            truncate_to: "week",
            tz_offset: Some(2),
            expected: Some("2024-02-19T00:02:00Z"),
            description: "week truncation on Monday with -2 minutes staying in same week",
        },
        TruncateTestCase {
            timestamp: "2024-02-19T00:02:00Z", // Monday
            truncate_to: "week",
            tz_offset: Some(3),
            expected: Some("2024-02-12T00:03:00Z"),
            description: "week truncation on Monday with -3 minutes crossing to previous week",
        },
        TruncateTestCase {
            timestamp: "2024-02-25T23:58:00Z", // Sunday
            truncate_to: "week",
            tz_offset: Some(-1),
            expected: Some("2024-02-18T23:59:00Z"),
            description: "week truncation on Sunday with +1 minute staying in current week",
        },
        TruncateTestCase {
            timestamp: "2024-02-25T23:58:00Z", // Sunday
            truncate_to: "week",
            tz_offset: Some(-3),
            expected: Some("2024-02-25T23:57:00Z"),
            description: "week truncation on Sunday with +3 minutes crossing to next week",
        },
        TruncateTestCase {
            timestamp: "2024-02-21T12:30:00Z", // Wednesday
            truncate_to: "week",
            tz_offset: None,
            expected: Some("2024-02-19T00:00:00Z"),
            description: "week truncation mid-week without offset",
        },
        TruncateTestCase {
            timestamp: "2024-02-18T23:59:30-01:00", // Monday
            truncate_to: "week",
            tz_offset: None,
            expected: Some("2024-02-19T00:00:00Z"),
            description: "week truncation exactly on Monday boundary",
        },
        // Hour truncation
        TruncateTestCase {
            timestamp: "2024-02-29T22:58:11Z",
            truncate_to: "hour",
            tz_offset: Some(-61),
            expected: Some("2024-02-29T21:59:00Z"),
            description: "hour truncation with -61 minutes crossing +1 hour",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T22:58:11Z",
            truncate_to: "hour",
            tz_offset: Some(-62),
            expected: Some("2024-02-29T22:58:00Z"),
            description: "hour truncation with -62 minutes crossing +2 hours",
        },
        // Minute truncation
        TruncateTestCase {
            timestamp: "2024-03-01T00:07:11Z",
            truncate_to: "minute",
            tz_offset: Some(7),
            expected: Some("2024-03-01T00:07:00Z"),
            description: "minute truncation with -7 seconds",
        },
        TruncateTestCase {
            timestamp: "2024-03-01T00:07:11Z",
            truncate_to: "minute",
            tz_offset: Some(8),
            expected: Some("2024-03-01T00:07:00Z"),
            description: "minute truncation with -8 seconds",
        },
        // Second truncation
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:30.500Z",
            truncate_to: "second",
            tz_offset: Some(-500),
            expected: Some("2024-02-29T23:58:30Z"),
            description: "second truncation zeroing milliseconds",
        },
        TruncateTestCase {
            timestamp: "2024-02-29T23:58:59.999Z",
            truncate_to: "second",
            tz_offset: Some(-1000),
            expected: Some("2024-02-29T23:58:59Z"),
            description: "second truncation near minute boundary",
        },
        // Error expected
        TruncateTestCase {
            timestamp: "2024-02-29T23:45:00Z",
            truncate_to: "invalid_unit",
            tz_offset: None,
            expected: None,
            description: "invalid truncation unit",
        },
    ];

    #[test]
    fn test_truncate_with_timezone() {
        for test_case in TRUNCATE_TEST_CASES {
            let timestamp = OffsetDateTime::parse(
                test_case.timestamp,
                &time::format_description::well_known::Rfc3339,
            )
            .unwrap();

            let result = truncate_timestamp_with_tz_offset(
                timestamp,
                test_case.truncate_to,
                test_case.tz_offset,
            );

            match (result, test_case.expected) {
                (Ok(result_dt), Some(expected)) => {
                    let result_str = result_dt
                        .format(&time::format_description::well_known::Rfc3339)
                        .unwrap();
                    assert_eq!(
                        result_str, expected,
                        "Failed test: {}\nInput: {} with offset {:?} minutes\nExpected: {}, Got: {}",
                        test_case.description, test_case.timestamp, test_case.tz_offset,
                        expected, result_str
                    );
                }
                (Err(_), None) => {
                    // Error is expected
                    continue;
                }
                (Ok(result_dt), None) => {
                    panic!(
                        "Expected error but got success: {} for test case: {}",
                        result_dt
                            .format(&time::format_description::well_known::Rfc3339)
                            .unwrap(),
                        test_case.description
                    );
                }
                (Err(e), Some(expected)) => {
                    panic!(
                        "Expected success with '{}' but got error: {} for test case: {}",
                        expected, e, test_case.description
                    );
                }
            }
        }
    }
}

#[cfg(test)]
mod comparison_key {
    use crate::interpreter::expr::perform_sha256_hash;
    use util::Value;

    struct ComparisonKeyTestCase {
        input: Value,
        expected: String,
        description: &'static str,
    }

    fn get_test_cases() -> Vec<ComparisonKeyTestCase> {
        vec![
            ComparisonKeyTestCase {
                input: Value::Null,
                expected: "74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b".to_string(),
                description: "null value returns hashed string",
            },
            ComparisonKeyTestCase {
                input: Value::String("null".to_string()),
                expected: "f072cbec3bf8841871d4284230c5e983dc211a56837aed862487148f947d1a1f".to_string(),
                description: "string value returns hashed string",
            },
            ComparisonKeyTestCase {
                input: Value::String("test".to_string()),
                expected: "4d967a30111bf29f0eba01c448b375c1629b2fed01cdfcc3aed91f1b57d5dd5e".to_string(),
                description: "string value returns hashed string",
            },
            ComparisonKeyTestCase {
                input: Value::Number(42.into()),
                expected: "73475cb40a568e8da8a045ced110137e159f890ac4da883b6b17dc651b3a8049".to_string(),
                description: "number value returns hashed number",
            },
            ComparisonKeyTestCase {
                input: Value::Bool(true),
                expected: "b5bea41b6c623f7c09f1bf24dcae58ebab3c0cdd90ad966bc43a45b44867e12b".to_string(),
                description: "boolean value returns hashed string",
            },
            ComparisonKeyTestCase {
                input: Value::Array(vec![Value::String("test".to_string())]),
                expected: "ecfd160805b1b0481fd0793c745be3b45d2054582de1c4df5d9b8fa4d78e7fbc".to_string(),
                description: "array value returns hashed string",
            },
            ComparisonKeyTestCase {
                input: Value::Object(Default::default()),
                expected: "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a".to_string(),
                description: "empty object value returns hashed string",
            },
        ]
    }

    #[test]
    fn test_comparison_key() {
        for test_case in get_test_cases() {
            let result = perform_sha256_hash(test_case.input.clone());
            assert_eq!(
                result, test_case.expected,
                "Failed test: {}\nInput: {:?}\nExpected: {:?}, Got: {:?}",
                test_case.description, test_case.input, test_case.expected, result
            );
        }
    }
}
